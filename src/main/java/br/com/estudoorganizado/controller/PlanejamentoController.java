package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.CicloEstudoDTO;
import br.com.estudoorganizado.dto.PlanejamentoDTO;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.PlanejamentoService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/v1/planejamento")
@RequiredArgsConstructor
@Tag(name = "Planejamento", description = "Endpoints para gerenciar planejamentos de estudo")
public class PlanejamentoController {

    private final PlanejamentoService planejamentoService;

    @PostMapping
    public ResponseEntity<PlanejamentoDTO> saveOrUpdate(@RequestBody @Valid PlanejamentoDTO PlanejamentoDTO, @AuthenticationPrincipal User user) {
        return ResponseEntity.status(HttpStatus.CREATED).body(planejamentoService.saveOrUpdate(PlanejamentoDTO, user));
    }

    @GetMapping
    public ResponseEntity<PlanejamentoDTO> findByUserId(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(planejamentoService.findByUserId(user.getId()));
    }

    @PostMapping("/sugerir")
    public ResponseEntity<PlanejamentoDTO> sugerirCicloEstudo(@RequestBody @Valid PlanejamentoDTO planejamentoDTO, @AuthenticationPrincipal User user) {
        return ResponseEntity.ok(planejamentoService.sugerirPlanejamentoCicloEstudo(planejamentoDTO));
    }

}
