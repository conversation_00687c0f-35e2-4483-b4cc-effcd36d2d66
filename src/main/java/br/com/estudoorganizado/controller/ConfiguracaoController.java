package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.ConfiguracaoDTO;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.ConfiguracaoService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/v1/configuracoes")
@RequiredArgsConstructor
@Tag(name = "Configurações", description = "Endpoints para gerenciar configurações do usuário")
public class ConfiguracaoController {

    private final ConfiguracaoService configuracaoService;

    @GetMapping
    public ResponseEntity<ConfiguracaoDTO> getConfiguration(@AuthenticationPrincipal User user) {
        ConfiguracaoDTO config = configuracaoService.getConfiguration(user.getId());
        return ResponseEntity.ok(config);
    }

    @PutMapping
    public ResponseEntity<ConfiguracaoDTO> updateConfiguration(@AuthenticationPrincipal User user, @Valid @RequestBody ConfiguracaoDTO configuracaoDTO) {
        ConfiguracaoDTO updatedConfig = configuracaoService.updateConfiguration(user.getId(), configuracaoDTO);
        return ResponseEntity.ok(updatedConfig);
    }
} 