package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.DisciplinaDTO;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.DisciplinaService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/disciplinas")
@RequiredArgsConstructor
@Tag(name = "Disciplinas", description = "Endpoints para gerenciar disciplinas")
public class DisciplinaController {

    private final DisciplinaService disciplinaService;

    @PostMapping
    public ResponseEntity<DisciplinaDTO> save(@RequestBody @Valid DisciplinaDTO disciplinaDTO, @AuthenticationPrincipal User user) {
        return ResponseEntity.status(HttpStatus.CREATED).body(disciplinaService.save(disciplinaDTO, user));
    }

    @GetMapping
    public ResponseEntity<List<DisciplinaDTO>> findAll(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(disciplinaService.findAllByUserId(user.getId()));
    }

    @GetMapping("/{id}")
    public ResponseEntity<DisciplinaDTO> findById(@PathVariable Long id, @AuthenticationPrincipal User user) {
        return ResponseEntity.ok(disciplinaService.findByIdAndUserId(id, user.getId()));
    }

    @PutMapping("/{id}")
    public ResponseEntity<DisciplinaDTO> update(@PathVariable Long id, @RequestBody @Valid DisciplinaDTO disciplinaDTO, @AuthenticationPrincipal User user) {
        return ResponseEntity.ok(disciplinaService.update(id, user.getId(), disciplinaDTO));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id, @AuthenticationPrincipal User user) {
        disciplinaService.delete(id, user.getId());
        return ResponseEntity.noContent().build();
    }
}
