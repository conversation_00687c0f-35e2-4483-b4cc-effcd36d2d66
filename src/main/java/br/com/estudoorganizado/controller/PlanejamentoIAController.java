package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.PlanejamentoDTO;
import br.com.estudoorganizado.dto.PlanejamentoIARequestDTO;
import br.com.estudoorganizado.exception.GeminiServiceException;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.PlanejamentoInteligentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/v1/planejamento-ia")
@RequiredArgsConstructor
@Tag(name = "Planejamento IA", description = "Endpoints para criação de planejamentos usando Inteligência Artificial")
public class PlanejamentoIAController {
    
    private final PlanejamentoInteligentService planejamentoInteligentService;
    
    @PostMapping("/gerar")
    @Operation(
        summary = "Gerar planejamento com IA", 
        description = "Cria um planejamento de estudos personalizado usando IA baseado na descrição do usuário"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Planejamento criado com sucesso"),
        @ApiResponse(responseCode = "400", description = "Dados de entrada inválidos"),
        @ApiResponse(responseCode = "401", description = "Usuário não autenticado"),
        @ApiResponse(responseCode = "500", description = "Erro interno do servidor ou na comunicação com a IA")
    })
    public ResponseEntity<?> gerarPlanejamentoComIA(
            @RequestBody @Valid PlanejamentoIARequestDTO request,
            @AuthenticationPrincipal User user) {
        
        try {
            log.info("Iniciando geração de planejamento com IA para usuário: {}", user.getEmail());
            
            PlanejamentoDTO planejamento = planejamentoInteligentService.criarPlanejamentoComIA(request, user);
            
            log.info("Planejamento gerado com sucesso para usuário: {}", user.getEmail());
            return ResponseEntity.status(HttpStatus.CREATED).body(planejamento);
            
        } catch (IllegalArgumentException e) {
            log.warn("Dados inválidos fornecidos pelo usuário {}: {}", user.getEmail(), e.getMessage());
            return ResponseEntity.badRequest().body(new ErrorResponse("Dados inválidos: " + e.getMessage()));

        } catch (GeminiServiceException.ConfigurationException e) {
            log.error("Erro de configuração da IA para usuário {}: {}", user.getEmail(), e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ErrorResponse("Serviço de IA não configurado corretamente. Contate o administrador."));

        } catch (GeminiServiceException.CommunicationException e) {
            log.error("Erro de comunicação com IA para usuário {}: {}", user.getEmail(), e.getMessage());
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(new ErrorResponse("Serviço de IA temporariamente indisponível. Tente novamente em alguns minutos."));

        } catch (GeminiServiceException.InvalidResponseException e) {
            log.error("Resposta inválida da IA para usuário {}: {}", user.getEmail(), e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ErrorResponse("Erro ao processar resposta da IA. Tente reformular sua solicitação."));

        } catch (GeminiServiceException e) {
            log.error("Erro geral da IA para usuário {}: {}", user.getEmail(), e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ErrorResponse("Erro no serviço de IA: " + e.getMessage()));

        } catch (RuntimeException e) {
            log.error("Erro ao gerar planejamento com IA para usuário {}: {}", user.getEmail(), e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ErrorResponse("Erro interno: " + e.getMessage()));

        } catch (Exception e) {
            log.error("Erro inesperado ao gerar planejamento com IA para usuário {}: {}", user.getEmail(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ErrorResponse("Erro inesperado. Tente novamente."));
        }
    }
    
    @PostMapping("/sugestao")
    @Operation(
        summary = "Obter sugestão de planejamento", 
        description = "Obtém uma sugestão de planejamento sem salvar no banco de dados"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Sugestão gerada com sucesso"),
        @ApiResponse(responseCode = "400", description = "Dados de entrada inválidos"),
        @ApiResponse(responseCode = "401", description = "Usuário não autenticado"),
        @ApiResponse(responseCode = "500", description = "Erro interno do servidor ou na comunicação com a IA")
    })
    public ResponseEntity<?> obterSugestaoIA(
            @RequestBody @Valid PlanejamentoIARequestDTO request,
            @AuthenticationPrincipal User user) {
        
        try {
            log.info("Gerando sugestão de planejamento com IA para usuário: {}", user.getEmail());
            
            // Aqui você pode implementar uma versão que não salva no banco
            // Por enquanto, vamos usar o mesmo método mas retornar apenas a sugestão
            PlanejamentoDTO sugestao = planejamentoInteligentService.criarPlanejamentoComIA(request, user);
            
            log.info("Sugestão gerada com sucesso para usuário: {}", user.getEmail());
            return ResponseEntity.ok(sugestao);
            
        } catch (Exception e) {
            log.error("Erro ao gerar sugestão com IA para usuário {}: {}", user.getEmail(), e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ErrorResponse("Erro ao gerar sugestão: " + e.getMessage()));
        }
    }
    
    // Classe interna para respostas de erro
    public static class ErrorResponse {
        private String message;
        private long timestamp;
        
        public ErrorResponse(String message) {
            this.message = message;
            this.timestamp = System.currentTimeMillis();
        }
        
        public String getMessage() {
            return message;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
    }
}
