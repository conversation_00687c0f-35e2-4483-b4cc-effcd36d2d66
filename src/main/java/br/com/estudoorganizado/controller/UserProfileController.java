package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.UserProfileDTO;
import br.com.estudoorganizado.dto.UserProfileUpdateDTO;
import br.com.estudoorganizado.service.UserProfileService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.MediaType;

import java.io.IOException;

@RestController
@RequestMapping("/v1/profile")
@CrossOrigin(origins = "*")
@Tag(name = "User Profile", description = "Endpoints para gerenciar o perfil do usuário")
public class UserProfileController {

    @Autowired
    private UserProfileService userProfileService;

    @GetMapping
    public ResponseEntity<UserProfileDTO> getUserProfile() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String userEmail = authentication.getName();
        
        UserProfileDTO profile = userProfileService.getUserProfileByEmail(userEmail);
        return ResponseEntity.ok(profile);
    }

    @PutMapping(consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
    public ResponseEntity<UserProfileDTO> updateUserProfile(
            @RequestPart("profile") UserProfileUpdateDTO updateDTO,
            @RequestPart(value = "photo", required = false) MultipartFile photo) throws IOException {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String userEmail = authentication.getName();
        
        byte[] photoBytes = photo != null ? photo.getBytes() : null;

        UserProfileDTO updatedProfile = userProfileService.updateUserProfileByEmail(userEmail, updateDTO, photoBytes);
        return ResponseEntity.ok(updatedProfile);
    }
} 