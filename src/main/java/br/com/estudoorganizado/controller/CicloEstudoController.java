package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.*;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.CicloEstudoService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/ciclo-estudo")
@RequiredArgsConstructor
@Tag(name = "Ciclo de Estudo", description = "Endpoints para gerenciar ciclos de estudo")
public class CicloEstudoController {

    private final CicloEstudoService cicloEstudoService;

    @PostMapping
    public ResponseEntity<CicloEstudoDTO> save(@RequestBody @Valid CicloEstudoDTO cicloEstudoDTO, @AuthenticationPrincipal User user) {
        return ResponseEntity.status(HttpStatus.CREATED).body(cicloEstudoService.save(cicloEstudoDTO, user));
    }

    @GetMapping
    public ResponseEntity<List<CicloEstudoDTO>> findAll(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.findAllByUserId(user.getId()));
    }

    @GetMapping("/concluidos")
    public ResponseEntity<List<CicloEstudoDTO>> findAllConcluidos(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.findAllConcluidos(user.getId()));
    }

    @GetMapping("/{id}")
    public ResponseEntity<CicloEstudoDTO> findById(@PathVariable Long id, @AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.findByIdAndUserId(id, user.getId()));
    }

    @PutMapping("/{id}")
    public ResponseEntity<CicloEstudoDTO> update(@PathVariable Long id, @RequestBody @Valid CicloEstudoDTO cicloEstudoDTO, @AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.update(id, user, cicloEstudoDTO));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id, @AuthenticationPrincipal User user) {
        cicloEstudoService.delete(id, user.getId());
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/atual")
    public ResponseEntity<CicloEstudoAtualDTO> findCicloEstudoAtual(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.findCicloEstudoAtual(user.getId()));
    }

    @PostMapping("/registrar-estudo")
    public ResponseEntity<String> registrarEstudo(@RequestBody @Valid RegistroEstudoDTO registroEstudoDTO, @AuthenticationPrincipal User user) {
        cicloEstudoService.registrarEstudo(registroEstudoDTO, user);
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/desvincular-registro-estudo/{cicloEstudoDisciplinaId}")
    public ResponseEntity<String> desvincularRegistrosEstudo(@PathVariable Long cicloEstudoDisciplinaId, @AuthenticationPrincipal User user) {
        cicloEstudoService.desvincularRegistrosEstudo(cicloEstudoDisciplinaId, user);
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/reiniciar-ciclo-estudo/{cicloEstudoId}")
    public ResponseEntity<String> reiniciarCicloEstudo(@PathVariable Long cicloEstudoId, @AuthenticationPrincipal User user) {
        cicloEstudoService.reiniciarCicloEstudo(cicloEstudoId, user);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/indicadores-ciclo")
    public ResponseEntity<IndicadoresCicloDTO> indicadoresCiclos(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.indicadoresCiclos(user.getId()));
    }
}
