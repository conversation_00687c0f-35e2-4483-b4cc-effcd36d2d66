package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.UserProfileDTO;
import br.com.estudoorganizado.dto.UserProfileUpdateDTO;
import br.com.estudoorganizado.exception.ResourceNotFoundException;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class UserProfileService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    public UserProfileDTO getUserProfile(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("Usuário não encontrado"));

        UserProfileDTO profileDTO = new UserProfileDTO();
        profileDTO.setId(user.getId());
        profileDTO.setName(user.getName());
        profileDTO.setEmail(user.getEmail());
        profileDTO.setFoto(user.getFoto());

        return profileDTO;
    }

    public UserProfileDTO getUserProfileByEmail(String email) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("Usuário não encontrado"));

        UserProfileDTO profileDTO = new UserProfileDTO();
        profileDTO.setId(user.getId());
        profileDTO.setName(user.getName());
        profileDTO.setEmail(user.getEmail());
        profileDTO.setFoto(user.getFoto());

        return profileDTO;
    }

    public UserProfileDTO updateUserProfile(Long userId, UserProfileUpdateDTO updateDTO) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("Usuário não encontrado"));

        // Verificar se o email já existe para outro usuário
        if (!user.getEmail().equals(updateDTO.getEmail()) && 
            userRepository.existsByEmail(updateDTO.getEmail())) {
            throw new IllegalArgumentException("Email já está em uso");
        }

        // Atualizar dados básicos
        user.setName(updateDTO.getName());
        user.setEmail(updateDTO.getEmail());

        // Se uma nova senha foi fornecida, verificar a senha atual
        if (updateDTO.getNewPassword() != null && !updateDTO.getNewPassword().isEmpty()) {
            if (updateDTO.getCurrentPassword() == null || updateDTO.getCurrentPassword().isEmpty()) {
                throw new IllegalArgumentException("Senha atual é obrigatória para alterar a senha");
            }

            if (!passwordEncoder.matches(updateDTO.getCurrentPassword(), user.getPassword())) {
                throw new IllegalArgumentException("Senha atual incorreta");
            }

            user.setPassword(passwordEncoder.encode(updateDTO.getNewPassword()));
        }

        User savedUser = userRepository.save(user);

        UserProfileDTO profileDTO = new UserProfileDTO();
        profileDTO.setId(savedUser.getId());
        profileDTO.setName(savedUser.getName());
        profileDTO.setEmail(savedUser.getEmail());
        profileDTO.setFoto(savedUser.getFoto());

        return profileDTO;
    }

    public UserProfileDTO updateUserProfileByEmail(String email, UserProfileUpdateDTO updateDTO, byte[] foto) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("Usuário não encontrado"));

        // Verificar se o email já existe para outro usuário
        if (!user.getEmail().equals(updateDTO.getEmail()) && 
            userRepository.existsByEmail(updateDTO.getEmail())) {
            throw new IllegalArgumentException("Email já está em uso");
        }

        // Atualizar dados básicos
        user.setName(updateDTO.getName());
        user.setEmail(updateDTO.getEmail());
        
        if (foto != null) {
            user.setFoto(foto);
        }

        // Se uma nova senha foi fornecida, verificar a senha atual
        if (updateDTO.getNewPassword() != null && !updateDTO.getNewPassword().isEmpty()) {
            if (updateDTO.getCurrentPassword() == null || updateDTO.getCurrentPassword().isEmpty()) {
                throw new IllegalArgumentException("Senha atual é obrigatória para alterar a senha");
            }

            if (!passwordEncoder.matches(updateDTO.getCurrentPassword(), user.getPassword())) {
                throw new IllegalArgumentException("Senha atual incorreta");
            }

            user.setPassword(passwordEncoder.encode(updateDTO.getNewPassword()));
        }

        User savedUser = userRepository.save(user);

        UserProfileDTO profileDTO = new UserProfileDTO();
        profileDTO.setId(savedUser.getId());
        profileDTO.setName(savedUser.getName());
        profileDTO.setEmail(savedUser.getEmail());
        profileDTO.setFoto(savedUser.getFoto());

        return profileDTO;
    }
} 