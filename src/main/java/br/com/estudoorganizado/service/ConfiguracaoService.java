package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.ConfiguracaoDTO;
import br.com.estudoorganizado.exception.ResourceNotFoundException;
import br.com.estudoorganizado.model.Configuracao;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.repository.ConfiguracaoRepository;
import br.com.estudoorganizado.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

@Service
@RequiredArgsConstructor
public class ConfiguracaoService {

    private final ConfiguracaoRepository configuracaoRepository;
    private final UserRepository userRepository;

    @Transactional
    public ConfiguracaoDTO getConfiguration(Long userId) {
        Configuracao config = findOrCreateConfigByUserId(userId);
        return new ConfiguracaoDTO(config.getIntervalosRevisao());
    }

    @Transactional
    public ConfiguracaoDTO updateConfiguration(Long userId, ConfiguracaoDTO configuracaoDTO) {
        // Validação simples dos intervalos
        validateIntervalos(configuracaoDTO.getIntervalosRevisao());

        Configuracao config = findOrCreateConfigByUserId(userId);
        config.setIntervalosRevisao(configuracaoDTO.getIntervalosRevisao());
        Configuracao savedConfig = configuracaoRepository.save(config);
        return new ConfiguracaoDTO(savedConfig.getIntervalosRevisao());
    }
    
    private Configuracao findOrCreateConfigByUserId(Long userId) {
        return configuracaoRepository.findByUserId(userId).orElseGet(() -> {
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new ResourceNotFoundException("Usuário não encontrado com id: " + userId));
            Configuracao newConfig = new Configuracao(user);
            return configuracaoRepository.save(newConfig);
        });
    }

    @Transactional
    public int[] getIntervalosRevisaoAsArray(Long userId) {
        Configuracao config = findOrCreateConfigByUserId(userId);
        String[] intervalosStr = config.getIntervalosRevisao().split(",");
        return Arrays.stream(intervalosStr)
                     .map(String::trim)
                     .mapToInt(Integer::parseInt)
                     .toArray();
    }

    private void validateIntervalos(String intervalos) {
        try {
            Arrays.stream(intervalos.split(","))
                  .map(String::trim)
                  .forEach(Integer::parseInt);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Formato de intervalos de revisão inválido. Use números separados por vírgula.");
        }
    }
} 