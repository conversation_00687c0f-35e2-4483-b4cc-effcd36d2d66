package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.ConversaAssistenteDTO;
import br.com.estudoorganizado.dto.MensagemConversaDTO;
import br.com.estudoorganizado.model.ConversaAssistenteIA;
import br.com.estudoorganizado.model.MensagemConversa;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.repository.ConversaAssistenteIARepository;
import br.com.estudoorganizado.repository.MensagemConversaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConversaAssistenteAiService {
    
    private final ConversaAssistenteIARepository conversaRepository;
    private final MensagemConversaRepository mensagemRepository;
    
    /**
     * Cria uma nova conversa para o usuário
     */
    @Transactional
    public ConversaAssistenteIA criarNovaConversa(User user, String titulo) {
        log.info("Criando nova conversa para usuário: {} com título: {}", user.getEmail(), titulo);
        
        ConversaAssistenteIA conversa = ConversaAssistenteIA.builder()
                .user(user)
                .titulo(titulo != null ? titulo : "Nova Conversa")
                .build();
        
        return conversaRepository.save(conversa);
    }
    
    /**
     * Adiciona uma mensagem à conversa
     */
    @Transactional
    public MensagemConversa adicionarMensagem(ConversaAssistenteIA conversa, String conteudo, MensagemConversa.TipoMensagem tipo, User user) {
        log.info("Adicionando mensagem do tipo {} à conversa {}", tipo, conversa.getId());
        
        MensagemConversa mensagem = MensagemConversa.builder()
                .conversa(conversa)
                .conteudo(conteudo)
                .tipo(tipo)
                .user(user)
                .build();
        
        MensagemConversa mensagemSalva = mensagemRepository.save(mensagem);
        
        // Atualiza a data da última mensagem na conversa
        conversaRepository.save(conversa);
        
        return mensagemSalva;
    }
    
    /**
     * Busca uma conversa por ID e usuário
     */
    public Optional<ConversaAssistenteIA> buscarConversaPorId(Long conversaId, User user) {
        return conversaRepository.findByIdAndUser(conversaId, user);
    }
    
    /**
     * Lista todas as conversas do usuário
     */
    @Transactional(readOnly = true)
    public List<ConversaAssistenteDTO> listarConversasDoUsuario(User user) {
        List<ConversaAssistenteIA> conversas = conversaRepository.findByUserOrderByDataUltimaMensagemDesc(user);

        return conversas.stream()
                .map(this::converterParaDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Busca o histórico completo de uma conversa
     */
    @Transactional(readOnly = true)
    public ConversaAssistenteDTO buscarHistoricoCompleto(Long conversaId, User user) {
        Optional<ConversaAssistenteIA> conversaOpt = conversaRepository.findByIdAndUser(conversaId, user);

        if (conversaOpt.isEmpty()) {
            return null;
        }

        ConversaAssistenteIA conversa = conversaOpt.get();
        List<MensagemConversa> mensagens = mensagemRepository.findByConversaOrderByDataEnvioAsc(conversa);

        return converterParaDTOCompleto(conversa, mensagens);
    }
    
    /**
     * Busca as últimas N mensagens de uma conversa para contexto
     */
    @Transactional(readOnly = true)
    public List<MensagemConversaDTO> buscarUltimasMensagens(ConversaAssistenteIA conversa, int limite) {
        Pageable pageable = PageRequest.of(0, limite);
        List<MensagemConversa> mensagens = mensagemRepository.findTopNByConversaOrderByDataEnvioDesc(conversa.getId(), pageable);

        // Inverte a ordem para ficar cronológica
        return mensagens.stream()
                .sorted((m1, m2) -> m1.getDataEnvio().compareTo(m2.getDataEnvio()))
                .map(this::converterMensagemParaDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Gera um título automático baseado na primeira mensagem
     */
    public String gerarTituloAutomatico(String primeiraMensagem) {
        if (primeiraMensagem == null || primeiraMensagem.trim().isEmpty()) {
            return "Nova Conversa";
        }
        
        String titulo = primeiraMensagem.trim();
        if (titulo.length() > 50) {
            titulo = titulo.substring(0, 47) + "...";
        }
        
        return titulo;
    }
    
    /**
     * Atualiza o título de uma conversa
     */
    @Transactional
    public void atualizarTitulo(ConversaAssistenteIA conversa, String novoTitulo) {
        conversa.setTitulo(novoTitulo);
        conversaRepository.save(conversa);
    }
    
    /**
     * Remove uma conversa e todas suas mensagens
     */
    @Transactional
    public void removerConversa(Long conversaId, User user) {
        Optional<ConversaAssistenteIA> conversaOpt = conversaRepository.findByIdAndUser(conversaId, user);
        
        if (conversaOpt.isPresent()) {
            ConversaAssistenteIA conversa = conversaOpt.get();
            mensagemRepository.deleteByConversa(conversa);
            conversaRepository.delete(conversa);
            log.info("Conversa {} removida para usuário {}", conversaId, user.getEmail());
        }
    }
    
    private ConversaAssistenteDTO converterParaDTO(ConversaAssistenteIA conversa) {
        // Busca a última mensagem para preview
        Pageable pageable = PageRequest.of(0, 1);
        List<MensagemConversa> ultimaMensagem = mensagemRepository.findLastMessageByConversa(conversa.getId(), pageable);
        
        String preview = ultimaMensagem.isEmpty() ? "" : 
                        ultimaMensagem.get(0).getConteudo().length() > 100 ? 
                        ultimaMensagem.get(0).getConteudo().substring(0, 97) + "..." :
                        ultimaMensagem.get(0).getConteudo();
        
        return ConversaAssistenteDTO.builder()
                .id(conversa.getId())
                .titulo(conversa.getTitulo())
                .dataCriacao(conversa.getDataCriacao())
                .dataUltimaMensagem(conversa.getDataUltimaMensagem())
                .totalMensagens((int) mensagemRepository.countByConversa(conversa))
                .ultimaMensagem(preview)
                .build();
    }
    
    private ConversaAssistenteDTO converterParaDTOCompleto(ConversaAssistenteIA conversa, List<MensagemConversa> mensagens) {
        List<MensagemConversaDTO> mensagensDTO = mensagens.stream()
                .map(this::converterMensagemParaDTO)
                .collect(Collectors.toList());
        
        return ConversaAssistenteDTO.builder()
                .id(conversa.getId())
                .titulo(conversa.getTitulo())
                .dataCriacao(conversa.getDataCriacao())
                .dataUltimaMensagem(conversa.getDataUltimaMensagem())
                .totalMensagens(mensagens.size())
                .mensagens(mensagensDTO)
                .build();
    }
    
    private MensagemConversaDTO converterMensagemParaDTO(MensagemConversa mensagem) {
        return MensagemConversaDTO.builder()
                .id(mensagem.getId())
                .tipo(mensagem.getTipo())
                .conteudo(mensagem.getConteudo())
                .dataEnvio(mensagem.getDataEnvio())
                .build();
    }
}
