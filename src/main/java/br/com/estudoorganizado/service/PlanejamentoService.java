package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.CicloEstudoDTO;
import br.com.estudoorganizado.dto.PlanejamentoDTO;
import br.com.estudoorganizado.model.CicloEstudo;
import br.com.estudoorganizado.model.Planejamento;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.repository.PlanejamentoRepository;
import br.com.estudoorganizado.util.CicloEstudoUtil;
import br.com.estudoorganizado.util.Util;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class PlanejamentoService {

    private final PlanejamentoRepository planejamentoRepository;
    private final CicloEstudoService cicloEstudoService;
    private final ModelMapper modelMapper;

    public PlanejamentoDTO saveOrUpdate(PlanejamentoDTO dto, User user) {
        validarDados(dto);

        Planejamento planejamentoEntity = planejamentoRepository.findByUserId(user.getId())
                .orElse(null);

        if (dto.getCicloEstudo() != null) {
            CicloEstudoDTO cicloEstudoDTO;
            if (Util.emptyNumber(dto.getCicloEstudo().getId())) {
                cicloEstudoDTO = cicloEstudoService.save(dto.getCicloEstudo(), user);
            } else {
                cicloEstudoDTO = cicloEstudoService.update(dto.getCicloEstudo().getId(), user, dto.getCicloEstudo());
            }
            dto.setCicloEstudo(cicloEstudoDTO);
        }

        planejamentoEntity = planejamentoEntity == null ? new Planejamento() : planejamentoEntity;
        planejamentoEntity.setNome(dto.getNome());
        planejamentoEntity.setUser(user);
        planejamentoEntity.setDataCriacao(dto.getDataCriacao() != null ? dto.getDataCriacao() : LocalDateTime.now());
        if (dto.getCicloEstudo() != null && !Util.emptyNumber(dto.getCicloEstudo().getId())) {
            CicloEstudo cicloEstudo = cicloEstudoService.findById(dto.getCicloEstudo().getId());
            if (cicloEstudo != null) {
                planejamentoEntity.setCicloEstudo(cicloEstudo);
            }
        }
        planejamentoEntity.setHorasDisponiveisPorSemana(dto.getHorasDisponiveisPorSemana());
        planejamentoEntity.setMinutosDuracaoMaximaPorSessao(dto.getMinutosDuracaoMaximaPorSessao());
        planejamentoEntity.setIntervalosRevisao(dto.getIntervalosRevisao());

        Planejamento savedEntity = planejamentoRepository.save(planejamentoEntity);
        return modelMapper.map(savedEntity, PlanejamentoDTO.class);
    }

    public PlanejamentoDTO findByUserId(Long userId) {
        Planejamento planejamento = planejamentoRepository.findByUserId(userId)
                .orElse(null);
        if (planejamento == null) {
            return null;
        }
        return modelMapper.map(planejamento, PlanejamentoDTO.class);
    }

    private void validarDados(PlanejamentoDTO dto) {
        if (dto == null) {
            throw new IllegalArgumentException("Planejamento não informado!");
        }
        if (Util.emptyString(dto.getNome())) {
            throw new IllegalArgumentException("Nome da planejamento não foi informado!");
        }
        if (Util.emptyNumber(dto.getHorasDisponiveisPorSemana())) {
            throw new IllegalArgumentException("Horas disponíveis por semana para estudar não foram informados!");
        }
        if (Util.emptyNumber(dto.getMinutosDuracaoMaximaPorSessao())) {
            throw new IllegalArgumentException("Duração máxima por sessão de estudo não foi informada!");
        }
        if (Util.emptyString(dto.getIntervalosRevisao())) {
            throw new IllegalArgumentException("Intervalos revisão espaçada não foram informados");
        }
    }

    /**
     * Gera sugestão automática de ciclo de estudo baseado nas regras de planejamento.
     *
     * A nova implementação:
     * 1. Distribui o tempo proporcionalmente baseado em peso e nível de conhecimento
     * 2. Cria sessões de estudo alternadas entre disciplinas
     * 3. Garante o uso total das horas semanais disponíveis
     * 4. Respeita a duração máxima por sessão
     */
    public PlanejamentoDTO sugerirPlanejamentoCicloEstudo(PlanejamentoDTO planejamentoDTO) {
        if (planejamentoDTO == null || planejamentoDTO.getCicloEstudo() == null ||
            planejamentoDTO.getCicloEstudo().getDisciplinas() == null ||
            planejamentoDTO.getCicloEstudo().getDisciplinas().isEmpty()) {
            throw new IllegalArgumentException("Disciplinas não informadas para sugestão de ciclo de estudo.");
        }

        double horasSemanais = planejamentoDTO.getHorasDisponiveisPorSemana() != null ?
            planejamentoDTO.getHorasDisponiveisPorSemana() : 10.0;
        int duracaoMaximaSessao = planejamentoDTO.getMinutosDuracaoMaximaPorSessao() != null ?
            planejamentoDTO.getMinutosDuracaoMaximaPorSessao() : 90;

        // Gerar ciclo de estudo otimizado aplicando diretamente às disciplinas
        CicloEstudoUtil.gerarCicloEstudo(
            planejamentoDTO.getCicloEstudo().getDisciplinas(),
            horasSemanais,
            duracaoMaximaSessao
        );

        return planejamentoDTO;
    }

}
