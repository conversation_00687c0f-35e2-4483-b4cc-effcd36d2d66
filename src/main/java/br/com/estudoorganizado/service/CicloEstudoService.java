package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.*;
import br.com.estudoorganizado.exception.ResourceNotFoundException;
import br.com.estudoorganizado.model.*;
import br.com.estudoorganizado.repository.CicloEstudoDisciplinaRepository;
import br.com.estudoorganizado.repository.CicloEstudoRepository;
import br.com.estudoorganizado.repository.DisciplinaRepository;
import br.com.estudoorganizado.repository.RegistroEstudoRepository;
import br.com.estudoorganizado.util.Util;
import lombok.RequiredArgsConstructor;
import org.hibernate.service.spi.ServiceException;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CicloEstudoService {

    private final CicloEstudoRepository cicloEstudoRepository;
    private final CicloEstudoDisciplinaRepository cicloEstudoDisciplinaRepository;
    private final DisciplinaRepository disciplinaRepository;
    private final RegistroEstudoRepository registroEstudoRepository;
    private final RevisaoEspacadaService revisaoEspacadaService;
    private final ModelMapper modelMapper;

    public CicloEstudoDTO save(CicloEstudoDTO dto, User user) {
        validarDados(dto);
        CicloEstudo cicloEstudoEntity = modelMapper.map(dto, CicloEstudo.class);
        cicloEstudoEntity.setUser(user);
        cicloEstudoEntity.setDataInicio(LocalDateTime.now());
        cicloEstudoEntity.getDisciplinas().forEach(d -> {
            d.setCicloEstudo(cicloEstudoEntity);
            d.setUser(user);
        });
        CicloEstudo savedCicloEstudo = cicloEstudoRepository.save(cicloEstudoEntity);
        return modelMapper.map(savedCicloEstudo, CicloEstudoDTO.class);
    }

    public List<CicloEstudoDTO> findAllByUserId(Long userId) {
        return cicloEstudoRepository.findByUserId(userId).stream()
                .map(CicloEstudo -> modelMapper.map(CicloEstudo, CicloEstudoDTO.class))
                .collect(Collectors.toList());
    }

    public List<CicloEstudoDTO> findAllConcluidos(Long userId) {
        return cicloEstudoRepository.findByUserIdAndConcluidoTrue(userId).stream()
                .map(CicloEstudo -> modelMapper.map(CicloEstudo, CicloEstudoDTO.class))
                .collect(Collectors.toList());
    }

    public CicloEstudoDTO findByIdAndUserId(Long id, Long userId) {
        CicloEstudo CicloEstudo = cicloEstudoRepository.findByIdAndUserId(id, userId)
                .orElseThrow(() -> new ResourceNotFoundException("CicloEstudo não encontrada com o id: " + id));
        return modelMapper.map(CicloEstudo, CicloEstudoDTO.class);
    }

    public CicloEstudo findById(Long id) {
        return cicloEstudoRepository.findById(id)
                .orElse(null);
    }

    public CicloEstudoDTO update(Long id, User user, CicloEstudoDTO dto) {
        validarDados(dto);
        CicloEstudo existingCicloEstudo = cicloEstudoRepository.findByIdAndUserId(id, user.getId())
                .orElseThrow(() -> new ResourceNotFoundException("CicloEstudo não encontrada com o id: " + id));

        existingCicloEstudo.setNome(dto.getNome());
        Set<CicloEstudoDisciplina> ceDisciplinas = dto.getDisciplinas().stream()
                .map(d -> {
                    CicloEstudoDisciplina ced = modelMapper.map(d, CicloEstudoDisciplina.class);
                    ced.setCicloEstudo(existingCicloEstudo);
                    ced.setUser(existingCicloEstudo.getUser());
                    return ced;
                }).collect(Collectors.toSet());
        existingCicloEstudo.getDisciplinas().clear();
        existingCicloEstudo.getDisciplinas().addAll(ceDisciplinas);
        CicloEstudo updatedCicloEstudo = cicloEstudoRepository.save(existingCicloEstudo);
        return modelMapper.map(updatedCicloEstudo, CicloEstudoDTO.class);
    }

    public void delete(Long id, Long userId) {
        if (!cicloEstudoRepository.existsByIdAndUserId(id, userId)) {
            throw new ResourceNotFoundException("CicloEstudo não encontrado com o id: " + id);
        }
        cicloEstudoRepository.deleteById(id);
    }

    public void validarDados(CicloEstudoDTO dto) {
        if (dto == null) {
            throw new IllegalArgumentException("CicloEstudo não informado!");
        }
        if (Objects.isNull(dto.getNome()) || dto.getNome().isEmpty()) {
            throw new IllegalArgumentException("Nome da CicloEstudo não foi informado!");
        }
    }

    public CicloEstudoAtualDTO findCicloEstudoAtual(Long userId) {
        CicloEstudo cicloEstudo = cicloEstudoRepository.findTopByUserIdOrderByIdDesc(userId)
                .orElseThrow(() -> new ResourceNotFoundException("Não foi encontrado Ciclo de Estudo para o usuário com id: " + userId));

        CicloEstudoAtualDTO cicloEstudoAtualDTO = new CicloEstudoAtualDTO();
        cicloEstudoAtualDTO.setDisciplinas(new ArrayList<>());
        cicloEstudoAtualDTO.setCicloEstudoId(cicloEstudo.getId());
        cicloEstudoAtualDTO.setNomeCicloEstudo(cicloEstudo.getNome());

        cicloEstudo.getDisciplinas().forEach(ced -> {
            DisciplinaEstudoDTO disciplinaEstudoDTO = new DisciplinaEstudoDTO();
            disciplinaEstudoDTO.setCicloEstudoDisciplinaId(ced.getId());
            disciplinaEstudoDTO.setDisciplinaId(ced.getDisciplina().getId());
            disciplinaEstudoDTO.setNomeDisciplina(ced.getDisciplina().getNome());
            disciplinaEstudoDTO.setOrdem(ced.getOrdem());
            if (ced.getTempoEstudoMeta() != null) {
                disciplinaEstudoDTO.setTempoEstudoMeta(Util.getHoraFormatada(ced.getTempoEstudoMeta(), "HH:mm:ss"));
            } else {
                disciplinaEstudoDTO.setTempoEstudoMeta("00:00:00");
            }

            LocalTime tempoTotalEstudo = LocalTime.MIN;
            List<RegistroEstudo> registrosEstudos = registroEstudoRepository.findByCicloEstudoDisciplinaId(ced.getId());
            for (RegistroEstudo re : registrosEstudos) {
                tempoTotalEstudo = tempoTotalEstudo
                        .plusHours(re.getTempoEstudado().getHour())
                        .plusMinutes(re.getTempoEstudado().getMinute())
                        .plusSeconds(re.getTempoEstudado().getSecond());
                disciplinaEstudoDTO.setDataUltimoEstudo(Util.getDataFormatoBR(re.getDataRegistro()));
            }
            disciplinaEstudoDTO.setTempoEstudado(Util.getHoraFormatada(tempoTotalEstudo, "HH:mm:ss"));
            cicloEstudoAtualDTO.getDisciplinas().add(disciplinaEstudoDTO);
        });
        Collections.sort(cicloEstudoAtualDTO.getDisciplinas(), Comparator.comparing(DisciplinaEstudoDTO::getOrdem));
        return cicloEstudoAtualDTO;
    }

    public void registrarEstudo(RegistroEstudoDTO reDTO, User user) throws ServiceException {
        Disciplina disciplinaEntity = null;
        CicloEstudoDisciplina cicloEstudoDisciplinaEntity = null;


        if (!Util.emptyNumber(reDTO.getCicloEstudoDisciplinaId())) {
            cicloEstudoDisciplinaEntity = cicloEstudoDisciplinaRepository
                    .findByIdAndUserId(reDTO.getCicloEstudoDisciplinaId(), user.getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Ciclo de Estudo não encontrado para o ID: " + reDTO.getCicloEstudoDisciplinaId()));
            disciplinaEntity = cicloEstudoDisciplinaEntity.getDisciplina();
        } else {
            disciplinaEntity = disciplinaRepository
                    .findByIdAndUserId(reDTO.getDisciplinaId(), user.getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Disciplina não encontrada para o ID: " + reDTO.getDisciplinaId()));
        }

        RegistroEstudo reEntity = new RegistroEstudo();
        reEntity.setUser(user);
        reEntity.setDescricaoEstudo(reDTO.getDescricaoEstudo());
        reEntity.setCicloEstudoDisciplina(cicloEstudoDisciplinaEntity);
        reEntity.setDisciplina(disciplinaEntity);
        reEntity.setTempoEstudado(reDTO.getTempoEstudado());
        reEntity.setDataRegistro(reDTO.getDataRegistro());
        reEntity.setQtdQuestoesAcertos(reDTO.getQtdQuestoesAcertos());
        reEntity.setQtdQuestoesErros(reDTO.getQtdQuestoesErros());

        RegistroEstudo savedRegistro = registroEstudoRepository.save(reEntity);
        
        // Gera revisões espaçadas se solicitado
        if (reDTO.getGerarRevisoesEspacadas() != null && reDTO.getGerarRevisoesEspacadas()) {
            revisaoEspacadaService.gerarRevisoesEspacadas(savedRegistro.getId(), user);
        }

        verificarConclusaoCicloEstudo(cicloEstudoDisciplinaEntity.getCicloEstudo());
    }

    private void verificarConclusaoCicloEstudo(CicloEstudo cicloEstudo) throws ServiceException {
        try {
            for (CicloEstudoDisciplina ced : cicloEstudo.getDisciplinas()) {
                List<RegistroEstudo> registroEstudos = registroEstudoRepository.findByCicloEstudoDisciplinaId(ced.getId());
                if (registroEstudos == null || registroEstudos.isEmpty()) {
                    return; // Ciclo de estudo não concluído se não houver registros
                }
            }
            // Finalizar ciclo atual
            cicloEstudo.setDataFim(LocalDateTime.now());
            cicloEstudo.setConcluido(true);
            cicloEstudoRepository.save(cicloEstudo);

            // Iniciar um novo ciclo de estudo a partir do ultimo
            CicloEstudo novoCicloEstudo = cicloEstudo.clone();
            novoCicloEstudo.setDataInicio(LocalDateTime.now());
            novoCicloEstudo.setCicloEstudoAnteriorId(cicloEstudo.getId());
            novoCicloEstudo.setDataFim(null);
            novoCicloEstudo.setConcluido(false);
            cicloEstudoRepository.save(novoCicloEstudo);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("Erro ao verificar conclusão do ciclo de estudo: " + e.getMessage(), e);
        }
    }

    public void desvincularRegistrosEstudo(Long cicloEstudoDisciplinaId, User user) {
        CicloEstudoDisciplina cedEntity = cicloEstudoDisciplinaRepository.findByIdAndUserId(cicloEstudoDisciplinaId, user.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Nenhum ciclo de estudo disciplina encontrado para o id: " + cicloEstudoDisciplinaId));

        List<RegistroEstudo> registroEstudos = registroEstudoRepository.findByCicloEstudoDisciplinaId(cedEntity.getId());
        if (registroEstudos == null) {
            return;
        }

        for (RegistroEstudo reEntity: registroEstudos) {
            reEntity.setCicloEstudoDisciplina(null);
            registroEstudoRepository.save(reEntity);
        }
    }

    public void reiniciarCicloEstudo(Long cicloEstudoId, User user) {
        CicloEstudo ceEntity = cicloEstudoRepository.findByIdAndUserId(cicloEstudoId, user.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Nenhum ciclo de estudo encontrado para o id: " + cicloEstudoId));

        ceEntity.getDisciplinas().forEach(cedEntity -> {
            List<RegistroEstudo> registrosEstudos = registroEstudoRepository.findByCicloEstudoDisciplinaId(cedEntity.getId());
            if (!Util.emptyList(registrosEstudos)) {
                registrosEstudos.forEach(re -> {
                    re.setCicloEstudoDisciplina(null);
                    registroEstudoRepository.save(re);
                });
            }
        });
    }

    public IndicadoresCicloDTO indicadoresCiclos(Long userId) {
        List<CicloEstudo> ciclos = cicloEstudoRepository.findByUserId(userId);

        IndicadoresCicloDTO indicadores = new IndicadoresCicloDTO();
        indicadores.setQtdCiclosConcluidos(ciclos.stream().filter(c -> c.getConcluido() != null && c.getConcluido()).toList().size());

        Integer tempoMinutosEstudou = 0;
        for (CicloEstudo c : ciclos) {
            for (CicloEstudoDisciplina ced : c.getDisciplinas()) {
                List<RegistroEstudo> registros = registroEstudoRepository.findByCicloEstudoDisciplinaId(ced.getId());
                for (RegistroEstudo r : registros) {
                    tempoMinutosEstudou += r.getTempoEstudado().getMinute() + r.getTempoEstudado().getHour() * 60;

                    if (r.getQtdQuestoesAcertos() != null) {
                        indicadores.setQtdQuestoesAcertos(indicadores.getQtdQuestoesAcertos() + r.getQtdQuestoesAcertos());
                    }
                    if (r.getQtdQuestoesErros() != null) {
                        indicadores.setQtdQuestoesErros(indicadores.getQtdQuestoesErros() + r.getQtdQuestoesErros());
                    }
                }
            }
        }
        indicadores.setQtdQuestoesResolvidas(indicadores.getQtdQuestoesAcertos() + indicadores.getQtdQuestoesErros());
        indicadores.setHorasEstudadasTotal(String.format("%02dh %02dmin", tempoMinutosEstudou/60, tempoMinutosEstudou%60));

        return indicadores;
    }
}
