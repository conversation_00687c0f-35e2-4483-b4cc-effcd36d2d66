package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.RevisaoEspacadaDTO;
import br.com.estudoorganizado.exception.ResourceNotFoundException;
import br.com.estudoorganizado.model.RegistroEstudo;
import br.com.estudoorganizado.model.RevisaoEspacada;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.repository.CicloEstudoDisciplinaRepository;
import br.com.estudoorganizado.repository.RegistroEstudoRepository;
import br.com.estudoorganizado.repository.RevisaoEspacadaRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RevisaoEspacadaService {

    private final RevisaoEspacadaRepository revisaoEspacadaRepository;
    private final RegistroEstudoRepository registroEstudoRepository;
    private final CicloEstudoDisciplinaRepository cicloEstudoDisciplinaRepository;
    private final ConfiguracaoService configuracaoService;

    /**
     * Algoritmo SM-2 modificado para revisão espaçada
     * Intervalos baseados em pesquisas científicas:
     * - 1ª revisão: 1 dia
     * - 2ª revisão: 3 dias
     * - 3ª revisão: 7 dias
     * - 4ª revisão: 14 dias
     * - 5ª revisão: 30 dias
     * - 6ª revisão: 60 dias
     * - 7ª revisão: 120 dias
     */
    //private static final int[] INTERVALOS_REVISAO = {1, 3, 7, 14, 30, 60, 120};

    public void gerarRevisoesEspacadas(Long registroEstudoId, User user) {
        RegistroEstudo registro = registroEstudoRepository.findByIdAndUserId(registroEstudoId, user.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Registro de estudo não encontrado"));

        final int[] intervalosRevisao = configuracaoService.getIntervalosRevisaoAsArray(user.getId());

        for (int i = 0; i < intervalosRevisao.length; i++) {
            RevisaoEspacada revisao = new RevisaoEspacada();
            revisao.setDataCriacao(registro.getDataRegistro());
            revisao.setDataRevisao(revisao.getDataCriacao().plusDays(intervalosRevisao[i]));
            revisao.setNivelDificuldade(3); // Nível médio inicial
            revisao.setDescricao("Revisão " + (i + 1) + " - " + registro.getDescricaoEstudo());
            revisao.setConcluida(false);
            revisao.setIntervaloDias(intervalosRevisao[i]);
            revisao.setRegistroEstudo(registro);
            revisao.setCicloEstudoDisciplina(registro.getCicloEstudoDisciplina());
            revisao.setUser(user);

            revisaoEspacadaRepository.save(revisao);
        }
    }

    public List<RevisaoEspacadaDTO> buscarRevisoesPendentes(Long userId) {
        LocalDateTime dataAtual = LocalDateTime.now();
        List<RevisaoEspacada> revisoes = revisaoEspacadaRepository.findRevisoesPendentesByUser(userId, dataAtual);
        
        return revisoes.stream()
                .map(this::converterParaDTO)
                .collect(Collectors.toList());
    }

    public List<RevisaoEspacadaDTO> buscarTodasRevisoesPendentes(Long userId) {
        List<RevisaoEspacada> revisoes = revisaoEspacadaRepository.findTodasRevisoesPendentesByUser(userId);
        
        return revisoes.stream()
                .map(this::converterParaDTO)
                .collect(Collectors.toList());
    }

    public void marcarComoConcluida(Long revisaoId, Integer nivelDificuldade, Long userId) {
        RevisaoEspacada revisao = revisaoEspacadaRepository.findByIdAndUserId(revisaoId, userId)
                .orElseThrow(() -> new ResourceNotFoundException("Revisão não encontrada"));
        revisao.setConcluida(true);
        revisao.setDataConclusao(LocalDateTime.now());
        revisao.setNivelDificuldade(nivelDificuldade);

        revisaoEspacadaRepository.save(revisao);

        // Se a dificuldade for alta (4-5), gera uma revisão adicional mais próxima
        if (nivelDificuldade >= 4) {
            gerarRevisaoAdicional(revisao);
        }
    }

    private void gerarRevisaoAdicional(RevisaoEspacada revisaoOriginal) {
        RevisaoEspacada revisaoAdicional = new RevisaoEspacada();
        revisaoAdicional.setDataCriacao(LocalDateTime.now());
        revisaoAdicional.setDataRevisao(LocalDateTime.now().plusDays(2)); // Revisão em 2 dias
        revisaoAdicional.setNivelDificuldade(3);
        revisaoAdicional.setDescricao("Revisão adicional - " + revisaoOriginal.getDescricao());
        revisaoAdicional.setConcluida(false);
        revisaoAdicional.setIntervaloDias(2);
        revisaoAdicional.setRegistroEstudo(revisaoOriginal.getRegistroEstudo());
        revisaoAdicional.setCicloEstudoDisciplina(revisaoOriginal.getCicloEstudoDisciplina());
        revisaoAdicional.setUser(revisaoOriginal.getUser());

        revisaoEspacadaRepository.save(revisaoAdicional);
    }

    public Long contarRevisoesPendentes(Long userId) {
        LocalDateTime dataAtual = LocalDateTime.now();
        return revisaoEspacadaRepository.countRevisoesPendentesByUser(userId, dataAtual);
    }

    public void removerRevisoesPorRegistroEstudo(Long registroEstudoId) {
        List<RevisaoEspacada> revisoes = revisaoEspacadaRepository.findRevisoesByRegistroEstudo(registroEstudoId);
        revisaoEspacadaRepository.deleteAll(revisoes);
    }

    private RevisaoEspacadaDTO converterParaDTO(RevisaoEspacada revisao) {
        return new RevisaoEspacadaDTO(
                revisao.getId(),
                revisao.getDataRevisao(),
                revisao.getDataCriacao(),
                revisao.getDataConclusao(),
                revisao.getNivelDificuldade(),
                revisao.getDescricao(),
                revisao.getConcluida(),
                revisao.getIntervaloDias(),
                revisao.getRegistroEstudo() != null ? revisao.getRegistroEstudo().getId() : null,
                revisao.getCicloEstudoDisciplina() != null ? revisao.getCicloEstudoDisciplina().getId() : null,
                revisao.getCicloEstudoDisciplina() != null && revisao.getCicloEstudoDisciplina().getDisciplina() != null 
                    ? revisao.getCicloEstudoDisciplina().getDisciplina().getNome() : null,
                revisao.getCicloEstudoDisciplina() != null && revisao.getCicloEstudoDisciplina().getCicloEstudo() != null 
                    ? revisao.getCicloEstudoDisciplina().getCicloEstudo().getNome() : null
        );
    }
} 