package br.com.estudoorganizado.util;

import br.com.estudoorganizado.dto.CicloEstudoDisciplinaDTO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Utilitário para geração de ciclos de estudo eficientes.
 * Implementa algoritmos para distribuição inteligente de tempo de estudo.
 */
public class CicloEstudoUtil {
    
    /**
     * Gera um ciclo de estudo otimizado criando sessões alternadas.
     *
     * @param disciplinas Lista de disciplinas com peso e nível de conhecimento (será modificada)
     * @param horasSemanais Horas totais disponíveis por semana
     * @param duracaoMaximaSessao Duração máxima em minutos por sessão de estudo
     */
    public static void gerarCicloEstudo(List<CicloEstudoDisciplinaDTO> disciplinas,
                                        double horasSemanais,
                                        int duracaoMaximaSessao) {

        if (disciplinas == null || disciplinas.isEmpty()) {
            return;
        }

        // Guardar disciplinas originais
        List<CicloEstudoDisciplinaDTO> disciplinasOriginais = new ArrayList<>(disciplinas);

        // 1. Calcular prioridades e distribuir tempo
        Map<CicloEstudoDisciplinaDTO, Integer> temposPorDisciplina = calcularDistribuicaoTempo(
            disciplinasOriginais, horasSemanais);

        // 2. Criar sessões alternadas
        List<CicloEstudoDisciplinaDTO> sessoesAlternadas = criarSessoesAlternadas(
            temposPorDisciplina, duracaoMaximaSessao);

        // 3. Substituir a lista original pelas sessões alternadas
        try {
            disciplinas.clear();
            disciplinas.addAll(sessoesAlternadas);
        } catch (UnsupportedOperationException e) {
            // Se a lista for imutável, não podemos modificá-la
            // Neste caso, apenas aplicamos os valores às disciplinas originais
            aplicarTemposEOrdemSimples(disciplinasOriginais, temposPorDisciplina, duracaoMaximaSessao);
        }
    }
    
    /**
     * Calcula a distribuição de tempo por disciplina baseada em peso e nível de conhecimento.
     */
    private static Map<CicloEstudoDisciplinaDTO, Integer> calcularDistribuicaoTempo(
            List<CicloEstudoDisciplinaDTO> disciplinas, double horasSemanais) {
        
        // Calcular prioridade total
        int totalPrioridade = disciplinas.stream()
            .mapToInt(d -> {
                int peso = d.getPeso() != null ? d.getPeso() : 1;
                int nivel = d.getNivelConhecimento() != null ? d.getNivelConhecimento() : 3;
                return peso * (6 - nivel); // Quanto menor o nível, maior a prioridade
            })
            .sum();
        
        Map<CicloEstudoDisciplinaDTO, Integer> distribuicao = new HashMap<>();
        int minutosSemanais = (int) (horasSemanais * 60);
        
        for (CicloEstudoDisciplinaDTO disciplina : disciplinas) {
            int peso = disciplina.getPeso() != null ? disciplina.getPeso() : 1;
            int nivel = disciplina.getNivelConhecimento() != null ? disciplina.getNivelConhecimento() : 3;
            int prioridade = peso * (6 - nivel);
            
            int minutosDisciplina = totalPrioridade > 0 ? 
                (prioridade * minutosSemanais) / totalPrioridade : 
                minutosSemanais / disciplinas.size();
                
            distribuicao.put(disciplina, minutosDisciplina);
        }
        
        return distribuicao;
    }
    
    /**
     * Cria sessões alternadas entre as disciplinas até completar as horas semanais.
     */
    private static List<CicloEstudoDisciplinaDTO> criarSessoesAlternadas(
            Map<CicloEstudoDisciplinaDTO, Integer> temposPorDisciplina,
            int duracaoMaximaSessao) {

        List<CicloEstudoDisciplinaDTO> sessoesAlternadas = new ArrayList<>();

        // Criar filas de sessões para cada disciplina
        Map<String, Queue<CicloEstudoDisciplinaDTO>> filasPorDisciplina = new HashMap<>();

        for (Map.Entry<CicloEstudoDisciplinaDTO, Integer> entry : temposPorDisciplina.entrySet()) {
            CicloEstudoDisciplinaDTO disciplinaOriginal = entry.getKey();
            int tempoTotal = entry.getValue();
            String nomeDisciplina = disciplinaOriginal.getDisciplina().getNome();

            Queue<CicloEstudoDisciplinaDTO> filaSessoes = new LinkedList<>();

            // Dividir o tempo total em sessões menores
            while (tempoTotal > 0) {
                int duracaoSessao = Math.min(tempoTotal, duracaoMaximaSessao);

                // Criar uma cópia da disciplina para esta sessão
                CicloEstudoDisciplinaDTO sessao = clonarDisciplina(disciplinaOriginal);
                sessao.setTempoEstudoMeta(br.com.estudoorganizado.util.Util.getLocalTimeHHmmss(
                    String.format("%02d:%02d:00", duracaoSessao / 60, duracaoSessao % 60)));

                filaSessoes.add(sessao);
                tempoTotal -= duracaoSessao;
            }

            filasPorDisciplina.put(nomeDisciplina, filaSessoes);
        }

        // Alternar entre as disciplinas até esgotar todas as sessões
        int ordem = 1;
        while (!filasPorDisciplina.isEmpty()) {
            Iterator<Map.Entry<String, Queue<CicloEstudoDisciplinaDTO>>> iterator =
                filasPorDisciplina.entrySet().iterator();

            while (iterator.hasNext()) {
                Map.Entry<String, Queue<CicloEstudoDisciplinaDTO>> entry = iterator.next();
                Queue<CicloEstudoDisciplinaDTO> filaSessoes = entry.getValue();

                if (!filaSessoes.isEmpty()) {
                    CicloEstudoDisciplinaDTO sessao = filaSessoes.poll();
                    sessao.setOrdem(ordem++);
                    sessoesAlternadas.add(sessao);
                }

                if (filaSessoes.isEmpty()) {
                    iterator.remove();
                }
            }
        }

        return sessoesAlternadas;
    }

    /**
     * Clona uma disciplina para criar uma nova sessão.
     */
    private static CicloEstudoDisciplinaDTO clonarDisciplina(CicloEstudoDisciplinaDTO original) {
        CicloEstudoDisciplinaDTO clone = new CicloEstudoDisciplinaDTO();
        clone.setDisciplina(original.getDisciplina());
        clone.setPeso(original.getPeso());
        clone.setNivelConhecimento(original.getNivelConhecimento());
        // ordem e tempoEstudoMeta serão definidos posteriormente
        return clone;
    }

    /**
     * Método de fallback para quando a lista de disciplinas é imutável.
     */
    private static void aplicarTemposEOrdemSimples(List<CicloEstudoDisciplinaDTO> disciplinas,
                                                   Map<CicloEstudoDisciplinaDTO, Integer> temposPorDisciplina,
                                                   int duracaoMaximaSessao) {

        // Ordenar disciplinas por prioridade (maior prioridade = menor ordem)
        List<Map.Entry<CicloEstudoDisciplinaDTO, Integer>> disciplinasOrdenadas =
            temposPorDisciplina.entrySet().stream()
                .sorted((e1, e2) -> Integer.compare(e2.getValue(), e1.getValue())) // Decrescente por tempo
                .collect(Collectors.toList());

        int ordem = 1;
        for (Map.Entry<CicloEstudoDisciplinaDTO, Integer> entry : disciplinasOrdenadas) {
            CicloEstudoDisciplinaDTO disciplina = entry.getKey();
            int tempoTotal = entry.getValue();

            // Calcular tempo médio por sessão (respeitando duração máxima)
            int numSessoes = (int) Math.ceil((double) tempoTotal / duracaoMaximaSessao);
            int tempoMedioPorSessao = numSessoes > 0 ? tempoTotal / numSessoes : tempoTotal;

            // Aplicar valores à disciplina
            disciplina.setOrdem(ordem++);
            disciplina.setTempoEstudoMeta(br.com.estudoorganizado.util.Util.getLocalTimeHHmmss(
                String.format("%02d:%02d:00", tempoMedioPorSessao / 60, tempoMedioPorSessao % 60)));
        }
    }

}
