package br.com.estudoorganizado.util;

import br.com.estudoorganizado.model.RegistroEstudo;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Set;

public class Util {


    public static String getDataAplicandoFormatacao(LocalDateTime data, String formato) {
        if (data == null) {
            return "";
        }
        DateTimeFormatter formatador = DateTimeFormatter.ofPattern(formato);
        return data.format(formatador);
    }

    public static String getDataFormatoBR(LocalDateTime data) {
        if (data == null) {
            return "";
        }
        DateTimeFormatter formatador = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
        return data.format(formatador);
    }

    public static String formatarHora(String hhMMss) {
        DateTimeFormatter parser = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime horario = LocalTime.parse(hhMMss, parser);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH'h' mm'min'");
        return horario.format(formatter);
    }

    public static String getHoraFormatada(LocalTime localTime, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return localTime.format(formatter);
    }

    public static String formatarHora(LocalTime localTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH'h' mm'min'");
        return localTime.format(formatter);
    }

    public static LocalTime getLocalTimeHHmmss(String hhmmss) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        return LocalTime.parse(hhmmss, formatter);
    }


    public static boolean emptyList(List re) {
        return re == null || re.isEmpty();
    }

    public static boolean emptySet(Set re) {
        return re == null || re.isEmpty();
    }

    public static boolean emptyNumber(Number n) {
        return n == null || n.intValue() == 0 || n.doubleValue() == 0.0 || n.longValue() == 0l;
    }

    public static boolean emptyString(String s) {
        return s == null || s.trim().isEmpty();
    }
}
