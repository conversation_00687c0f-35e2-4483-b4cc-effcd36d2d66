package br.com.estudoorganizado.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(name = "utilizar-spring-ai", havingValue = "true")
public class ChatClientConfig {

    @Bean
    public ChatClient chatClient(ChatClient.Builder chatClientBuilder) {
        // Log para debug
        System.out.println("Criando ChatClient...");

        return chatClientBuilder
            .defaultFunctions("cadastrarDisciplina", "criarPlanejamentoComIA", "salvarOuAtualizarPlanejamento")
            .defaultSystem("Você é um assistente especializado em planejamento de estudos para concursos públicos. " +
                "Você pode ajudar os usuários a cadastrar disciplinas, criar e gerenciar planejamentos de estudo. " +
                "Seja sempre educado, claro e objetivo em suas respostas. " +
                "Quando o usuário solicitar uma ação específica, use as funções disponíveis para executá-la.")
            .build();
    }
}
