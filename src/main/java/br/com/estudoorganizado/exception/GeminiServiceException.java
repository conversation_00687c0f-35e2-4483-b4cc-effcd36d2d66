package br.com.estudoorganizado.exception;

public class GeminiServiceException extends RuntimeException {
    
    public GeminiServiceException(String message) {
        super(message);
    }
    
    public GeminiServiceException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public static class CommunicationException extends GeminiServiceException {
        public CommunicationException(String message) {
            super("Erro de comunicação com a API Gemini: " + message);
        }
        
        public CommunicationException(String message, Throwable cause) {
            super("Erro de comunicação com a API Gemini: " + message, cause);
        }
    }
    
    public static class InvalidResponseException extends GeminiServiceException {
        public InvalidResponseException(String message) {
            super("Resposta inválida da API Gemini: " + message);
        }
        
        public InvalidResponseException(String message, Throwable cause) {
            super("Resposta inválida da API Gemini: " + message, cause);
        }
    }
    
    public static class ConfigurationException extends GeminiServiceException {
        public ConfigurationException(String message) {
            super("Erro de configuração da API Gemini: " + message);
        }
    }
}
