package br.com.estudoorganizado.model;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Entity
public class Planejamento {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String nome;
    private LocalDateTime dataCriacao;
    private Integer horasDisponiveisPorSemana;
    private Integer minutosDuracaoMaximaPorSessao;
    @Column(name = "intervalos_revisao", nullable = false)
    private String intervalosRevisao = "1,3,7,14,30,60,120";

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id", unique = true)
    private User user;

    @ManyToOne
    @JoinColumn(name = "ciclo_estudo_id")
    private CicloEstudo cicloEstudo;


}
