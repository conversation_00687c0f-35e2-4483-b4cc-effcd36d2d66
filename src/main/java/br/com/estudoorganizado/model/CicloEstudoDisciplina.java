package br.com.estudoorganizado.model;

import jakarta.persistence.*;

import java.time.LocalTime;

@Entity
public class CicloEstudoDisciplina implements Cloneable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Integer ordem;
    private Integer peso;
    private LocalTime tempoEstudoMeta;
    private Integer nivelConhecimento;

    @ManyToOne
    @JoinColumn(name = "cicloestudo_id", foreignKey = @ForeignKey(name = "fk_cicloestudodisciplina_cicloestudo"))
    @com.fasterxml.jackson.annotation.JsonBackReference
    @lombok.ToString.Exclude
    private CicloEstudo cicloEstudo;

    @ManyToOne
    @JoinColumn(name = "disciplina_id", foreignKey = @ForeignKey(name = "fk_cicloestudodisciplina_disciplina"))
    private Disciplina disciplina;

    @ManyToOne
    @JoinColumn(name = "user_id", foreignKey = @ForeignKey(name = "fk_cicloestudodisciplina_user"))
    private User user;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Integer getPeso() {
        return peso;
    }

    public void setPeso(Integer peso) {
        this.peso = peso;
    }

    public LocalTime getTempoEstudoMeta() {
        return tempoEstudoMeta;
    }

    public void setTempoEstudoMeta(LocalTime tempoEstudoMeta) {
        this.tempoEstudoMeta = tempoEstudoMeta;
    }

    public CicloEstudo getCicloEstudo() {
        return cicloEstudo;
    }

    public void setCicloEstudo(CicloEstudo cicloEstudo) {
        this.cicloEstudo = cicloEstudo;
    }

    public Disciplina getDisciplina() {
        return disciplina;
    }

    public void setDisciplina(Disciplina disciplina) {
        this.disciplina = disciplina;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Integer getNivelConhecimento() {
        return nivelConhecimento;
    }
    public void setNivelConhecimento(Integer nivelConhecimento) {
        this.nivelConhecimento = nivelConhecimento;
    }

    @Override
    protected CicloEstudoDisciplina clone() throws CloneNotSupportedException {
        CicloEstudoDisciplina cloned = (CicloEstudoDisciplina) super.clone();
        cloned.setId(null);
        return cloned;
    }
}
