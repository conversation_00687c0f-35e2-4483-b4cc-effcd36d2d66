package br.com.estudoorganizado.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Entity
@Getter
@Setter
public class RegistroEstudo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private LocalDateTime dataRegistro;
    private LocalTime tempoEstudado;
    private String descricaoEstudo;
    private Integer questoesResolvidas;
    private Integer qtdQuestoesAcertos;
    private Integer qtdQuestoesErros;

    @ManyToOne
    @JoinColumn(name = "disciplina_id", foreignKey = @ForeignKey(name = "fk_resgistroestudo_disciplina"))
    private Disciplina disciplina;

    @ManyToOne
    @JoinColumn(name = "ciclo_estudo_disciplina_id", foreignKey = @ForeignKey(name = "fk_resgistroestudo_cicloestudodisciplina"))
    private CicloEstudoDisciplina cicloEstudoDisciplina;

    @ManyToOne
    @JoinColumn(name = "user_id", foreignKey = @ForeignKey(name = "fk_registroestudo_user"))
    private User user;
}
