package br.com.estudoorganizado.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity
@Table(name = "conversa_assistente_ia")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConversaAssistenteIA {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(nullable = false)
    private String titulo;
    
    @Column(name = "data_criacao", nullable = false)
    private LocalDateTime dataCriacao;
    
    @Column(name = "data_ultima_mensagem")
    private LocalDateTime dataUltimaMensagem;
    
    @OneToMany(mappedBy = "conversa", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<MensagemConversa> mensagens;
    
    @PrePersist
    protected void onCreate() {
        dataCriacao = LocalDateTime.now();
        dataUltimaMensagem = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        dataUltimaMensagem = LocalDateTime.now();
    }
}
