package br.com.estudoorganizado.model;

import jakarta.persistence.*;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.stream.Collectors;

@Entity
public class CicloEstudo implements Cloneable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String nome;
    private LocalDateTime dataInicio;
    private LocalDateTime dataFim;
    private Boolean concluido;
    private Long cicloEstudoAnteriorId;

    @OneToMany(mappedBy = "cicloEstudo", cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, orphanRemoval = true)
    private Set<CicloEstudoDisciplina> disciplinas;

    @ManyToOne
    @JoinColumn(name = "user_id", foreignKey = @ForeignKey(name = "fk_cicloestudo_user"))
    private User user;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public LocalDateTime getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(LocalDateTime dataInicio) {
        this.dataInicio = dataInicio;
    }

    public LocalDateTime getDataFim() {
        return dataFim;
    }

    public void setDataFim(LocalDateTime dataFim) {
        this.dataFim = dataFim;
    }

    public Set<CicloEstudoDisciplina> getDisciplinas() {
        return disciplinas;
    }

    public void setDisciplinas(Set<CicloEstudoDisciplina> disciplinas) {
        this.disciplinas = disciplinas;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Long getCicloEstudoAnteriorId() {
        return cicloEstudoAnteriorId;
    }

    public void setCicloEstudoAnteriorId(Long cicloEstudoAnteriorId) {
        this.cicloEstudoAnteriorId = cicloEstudoAnteriorId;
    }

    public Boolean getConcluido() {
        return concluido;
    }

    public void setConcluido(Boolean concluido) {
        this.concluido = concluido;
    }

    @Override
    public CicloEstudo clone() throws CloneNotSupportedException {
        // clonar o ciclo de estudo atual que será persidstido como um novo ciclo de estudo
        CicloEstudo cloned = (CicloEstudo) super.clone();
        cloned.setId(null);
        if (this.disciplinas != null) {
            cloned.disciplinas = this.disciplinas.stream()
                    .map(disciplina -> {
                        try {
                            CicloEstudoDisciplina ced = disciplina.clone();
                            ced.setCicloEstudo(cloned);
                            return ced;
                        } catch (CloneNotSupportedException e) {
                            throw new RuntimeException("Failed to clone CicloEstudoDisciplina", e);
                        }
                    })
                    .collect(Collectors.toSet());

        }
        return cloned;
    }
}
