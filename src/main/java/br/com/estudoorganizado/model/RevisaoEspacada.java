package br.com.estudoorganizado.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "revisao_espacada")
public class RevisaoEspacada {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private LocalDateTime dataRevisao;
    private LocalDateTime dataCriacao;
    private LocalDateTime dataConclusao;
    private Integer nivelDificuldade; // 1-5 (1=fácil, 5=difícil)
    private String descricao;
    private Boolean concluida = false;
    private Integer intervaloDias; // Intervalo em dias para próxima revisão

    @ManyToOne
    @JoinColumn(name = "registro_estudo_id", foreignKey = @ForeignKey(name = "fk_revisaoespacada_registroestudo"))
    private RegistroEstudo registroEstudo;

    @ManyToOne
    @JoinColumn(name = "ciclo_estudo_disciplina_id", foreignKey = @ForeignKey(name = "fk_revisaoespacada_cicloestudodisciplina"))
    private CicloEstudoDisciplina cicloEstudoDisciplina;

    @ManyToOne
    @JoinColumn(name = "user_id", foreignKey = @ForeignKey(name = "fk_revisaoespacada_user"))
    private User user;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getDataRevisao() {
        return dataRevisao;
    }

    public void setDataRevisao(LocalDateTime dataRevisao) {
        this.dataRevisao = dataRevisao;
    }

    public LocalDateTime getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(LocalDateTime dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public LocalDateTime getDataConclusao() {
        return dataConclusao;
    }

    public void setDataConclusao(LocalDateTime dataConclusao) {
        this.dataConclusao = dataConclusao;
    }

    public Integer getNivelDificuldade() {
        return nivelDificuldade;
    }

    public void setNivelDificuldade(Integer nivelDificuldade) {
        this.nivelDificuldade = nivelDificuldade;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Boolean getConcluida() {
        return concluida;
    }

    public void setConcluida(Boolean concluida) {
        this.concluida = concluida;
    }

    public Integer getIntervaloDias() {
        return intervaloDias;
    }

    public void setIntervaloDias(Integer intervaloDias) {
        this.intervaloDias = intervaloDias;
    }

    public RegistroEstudo getRegistroEstudo() {
        return registroEstudo;
    }

    public void setRegistroEstudo(RegistroEstudo registroEstudo) {
        this.registroEstudo = registroEstudo;
    }

    public CicloEstudoDisciplina getCicloEstudoDisciplina() {
        return cicloEstudoDisciplina;
    }

    public void setCicloEstudoDisciplina(CicloEstudoDisciplina cicloEstudoDisciplina) {
        this.cicloEstudoDisciplina = cicloEstudoDisciplina;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
} 