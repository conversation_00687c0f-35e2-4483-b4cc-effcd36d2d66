package br.com.estudoorganizado.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "mensagem_conversa")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MensagemConversa {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "conversa_id", nullable = false)
    private ConversaAssistenteIA conversa;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TipoMensagem tipo;
    
    @Column(nullable = false, columnDefinition = "TEXT")
    private String conteudo;
    
    @Column(name = "data_envio", nullable = false)
    private LocalDateTime dataEnvio;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @PrePersist
    protected void onCreate() {
        dataEnvio = LocalDateTime.now();
    }
    
    public enum TipoMensagem {
        USER,
        ASSISTANT
    }
}
