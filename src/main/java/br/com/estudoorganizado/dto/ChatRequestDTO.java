package br.com.estudoorganizado.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatRequestDTO {
    
    @NotBlank(message = "A mensagem é obrigatória")
    private String mensagem;
    
    private Long conversaId;
    
    private String tituloConversa;
}
