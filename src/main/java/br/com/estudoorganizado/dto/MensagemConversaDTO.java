package br.com.estudoorganizado.dto;

import br.com.estudoorganizado.model.MensagemConversa;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MensagemConversaDTO {
    
    private Long id;
    private MensagemConversa.TipoMensagem tipo;
    private String conteudo;
    private LocalDateTime dataEnvio;
}
