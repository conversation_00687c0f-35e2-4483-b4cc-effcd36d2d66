package br.com.estudoorganizado.dto;

import java.time.LocalDateTime;

public class RevisaoEspacadaDTO {
    private Long id;
    private LocalDateTime dataRevisao;
    private LocalDateTime dataCriacao;
    private LocalDateTime dataConclusao;
    private Integer nivelDificuldade;
    private String descricao;
    private Boolean concluida;
    private Integer intervaloDias;
    private Long registroEstudoId;
    private Long cicloEstudoDisciplinaId;
    private String nomeDisciplina;
    private String nomeCicloEstudo;

    public RevisaoEspacadaDTO() {}

    public RevisaoEspacadaDTO(Long id, LocalDateTime dataRevisao, LocalDateTime dataCriacao, 
                             LocalDateTime dataConclusao, Integer nivelDificuldade, String descricao, 
                             Boolean concluida, Integer intervaloDias, Long registroEstudoId, 
                             Long cicloEstudoDisciplinaId, String nomeDisciplina, String nomeCicloEstudo) {
        this.id = id;
        this.dataRevisao = dataRevisao;
        this.dataCriacao = dataCriacao;
        this.dataConclusao = dataConclusao;
        this.nivelDificuldade = nivelDificuldade;
        this.descricao = descricao;
        this.concluida = concluida;
        this.intervaloDias = intervaloDias;
        this.registroEstudoId = registroEstudoId;
        this.cicloEstudoDisciplinaId = cicloEstudoDisciplinaId;
        this.nomeDisciplina = nomeDisciplina;
        this.nomeCicloEstudo = nomeCicloEstudo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getDataRevisao() {
        return dataRevisao;
    }

    public void setDataRevisao(LocalDateTime dataRevisao) {
        this.dataRevisao = dataRevisao;
    }

    public LocalDateTime getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(LocalDateTime dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public LocalDateTime getDataConclusao() {
        return dataConclusao;
    }

    public void setDataConclusao(LocalDateTime dataConclusao) {
        this.dataConclusao = dataConclusao;
    }

    public Integer getNivelDificuldade() {
        return nivelDificuldade;
    }

    public void setNivelDificuldade(Integer nivelDificuldade) {
        this.nivelDificuldade = nivelDificuldade;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Boolean getConcluida() {
        return concluida;
    }

    public void setConcluida(Boolean concluida) {
        this.concluida = concluida;
    }

    public Integer getIntervaloDias() {
        return intervaloDias;
    }

    public void setIntervaloDias(Integer intervaloDias) {
        this.intervaloDias = intervaloDias;
    }

    public Long getRegistroEstudoId() {
        return registroEstudoId;
    }

    public void setRegistroEstudoId(Long registroEstudoId) {
        this.registroEstudoId = registroEstudoId;
    }

    public Long getCicloEstudoDisciplinaId() {
        return cicloEstudoDisciplinaId;
    }

    public void setCicloEstudoDisciplinaId(Long cicloEstudoDisciplinaId) {
        this.cicloEstudoDisciplinaId = cicloEstudoDisciplinaId;
    }

    public String getNomeDisciplina() {
        return nomeDisciplina;
    }

    public void setNomeDisciplina(String nomeDisciplina) {
        this.nomeDisciplina = nomeDisciplina;
    }

    public String getNomeCicloEstudo() {
        return nomeCicloEstudo;
    }

    public void setNomeCicloEstudo(String nomeCicloEstudo) {
        this.nomeCicloEstudo = nomeCicloEstudo;
    }
} 