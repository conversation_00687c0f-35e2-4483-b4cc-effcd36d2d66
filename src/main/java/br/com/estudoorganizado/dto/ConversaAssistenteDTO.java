package br.com.estudoorganizado.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversaAssistenteDTO {
    
    private Long id;
    private String titulo;
    private LocalDateTime dataCriacao;
    private LocalDateTime dataUltimaMensagem;
    private int totalMensagens;
    private String ultimaMensagem;
    private List<MensagemConversaDTO> mensagens;
}
