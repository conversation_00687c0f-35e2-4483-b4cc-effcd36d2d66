package br.com.estudoorganizado.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
public class RegistroEstudoDTO {

    private LocalDateTime dataRegistro;
    private LocalTime tempoEstudado;
    private String descricaoEstudo;
    private Long disciplinaId;
    private Long cicloEstudoDisciplinaId;
    private Boolean gerarRevisoesEspacadas;
    private Integer qtdQuestoesAcertos = 0;
    private Integer qtdQuestoesErros = 0;

}
