package br.com.estudoorganizado.repository;

import br.com.estudoorganizado.model.RegistroEstudo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface RegistroEstudoRepository extends JpaRepository<RegistroEstudo, Long> {

    List<RegistroEstudo> findByCicloEstudoDisciplinaId(Long cicloEstudoDisciplinaId);

    Optional<RegistroEstudo> findByIdAndUserId(Long id, Long userId);

}
