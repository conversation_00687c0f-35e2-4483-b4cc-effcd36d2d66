package br.com.estudoorganizado.repository;

import br.com.estudoorganizado.model.RevisaoEspacada;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface RevisaoEspacadaRepository extends JpaRepository<RevisaoEspacada, Long> {

    @Query("SELECT r FROM RevisaoEspacada r WHERE r.user.id = :userId AND r.concluida = false AND r.dataRevisao <= :dataAtual ORDER BY r.dataRevisao ASC")
    List<RevisaoEspacada> findRevisoesPendentesByUser(@Param("userId") Long userId, @Param("dataAtual") LocalDateTime dataAtual);

    @Query("SELECT r FROM RevisaoEspacada r WHERE r.user.id = :userId AND r.concluida = false ORDER BY r.dataRevisao ASC")
    List<RevisaoEspacada> findTodasRevisoesPendentesByUser(@Param("userId") Long userId);

    @Query("SELECT r FROM RevisaoEspacada r WHERE r.cicloEstudoDisciplina.id = :cicloEstudoDisciplinaId AND r.concluida = false ORDER BY r.dataRevisao ASC")
    List<RevisaoEspacada> findRevisoesByCicloEstudoDisciplina(@Param("cicloEstudoDisciplinaId") Long cicloEstudoDisciplinaId);

    @Query("SELECT r FROM RevisaoEspacada r WHERE r.registroEstudo.id = :registroEstudoId")
    List<RevisaoEspacada> findRevisoesByRegistroEstudo(@Param("registroEstudoId") Long registroEstudoId);

    @Query("SELECT COUNT(r) FROM RevisaoEspacada r WHERE r.user.id = :userId AND r.concluida = false AND r.dataRevisao <= :dataAtual")
    Long countRevisoesPendentesByUser(@Param("userId") Long userId, @Param("dataAtual") LocalDateTime dataAtual);

    Optional<RevisaoEspacada> findByIdAndUserId(Long id, Long userId);
} 