package br.com.estudoorganizado.repository;

import br.com.estudoorganizado.model.ConversaAssistenteIA;
import br.com.estudoorganizado.model.MensagemConversa;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MensagemConversaRepository extends JpaRepository<MensagemConversa, Long> {
    
    /**
     * Busca todas as mensagens de uma conversa ordenadas por data de envio
     */
    List<MensagemConversa> findByConversaOrderByDataEnvioAsc(ConversaAssistenteIA conversa);
    
    /**
     * Busca mensagens de uma conversa com paginação
     */
    Page<MensagemConversa> findByConversaOrderByDataEnvioAsc(ConversaAssistenteIA conversa, Pageable pageable);
    
    /**
     * Busca as últimas N mensagens de uma conversa
     */
    @Query("SELECT m FROM MensagemConversa m WHERE m.conversa.id = :conversaId ORDER BY m.dataEnvio DESC")
    List<MensagemConversa> findTopNByConversaOrderByDataEnvioDesc(Long conversaId, Pageable pageable);
    
    /**
     * Conta o número de mensagens em uma conversa
     */
    long countByConversa(ConversaAssistenteIA conversa);
    
    /**
     * Busca a última mensagem de uma conversa
     */
    @Query("SELECT m FROM MensagemConversa m WHERE m.conversa.id = :conversaId ORDER BY m.dataEnvio DESC")
    List<MensagemConversa> findLastMessageByConversa(Long conversaId, Pageable pageable);
    
    /**
     * Remove todas as mensagens de uma conversa
     */
    void deleteByConversa(ConversaAssistenteIA conversa);
}
