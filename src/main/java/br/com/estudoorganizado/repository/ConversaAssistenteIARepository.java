package br.com.estudoorganizado.repository;

import br.com.estudoorganizado.model.ConversaAssistenteIA;
import br.com.estudoorganizado.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ConversaAssistenteIARepository extends JpaRepository<ConversaAssistenteIA, Long> {
    
    /**
     * Busca todas as conversas de um usuário ordenadas pela data da última mensagem
     */
    @Query("SELECT c FROM ConversaAssistenteIA c WHERE c.user = :user ORDER BY c.dataUltimaMensagem DESC")
    Page<ConversaAssistenteIA> findByUserOrderByDataUltimaMensagemDesc(@Param("user") User user, Pageable pageable);
    
    /**
     * Busca todas as conversas de um usuário ordenadas pela data da última mensagem
     */
    List<ConversaAssistenteIA> findByUserOrderByDataUltimaMensagemDesc(User user);
    
    /**
     * Busca uma conversa específica de um usuário
     */
    Optional<ConversaAssistenteIA> findByIdAndUser(Long id, User user);
    
    /**
     * Conta o número de conversas de um usuário
     */
    long countByUser(User user);
    
    /**
     * Busca conversas por título contendo texto (case insensitive)
     */
    @Query("SELECT c FROM ConversaAssistenteIA c WHERE c.user = :user AND LOWER(c.titulo) LIKE LOWER(CONCAT('%', :titulo, '%')) ORDER BY c.dataUltimaMensagem DESC")
    List<ConversaAssistenteIA> findByUserAndTituloContainingIgnoreCase(@Param("user") User user, @Param("titulo") String titulo);
}
