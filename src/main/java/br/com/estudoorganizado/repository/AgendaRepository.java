package br.com.estudoorganizado.repository;

import br.com.estudoorganizado.model.Agenda;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface AgendaRepository extends JpaRepository<Agenda, Long> {

    List<Agenda> findByUserIdAndDataHoraInicioBetween(Long userId, LocalDateTime start, LocalDateTime end);

    Optional<Agenda> findByIdAndUserId(Long id, Long userId);

    List<Agenda> findByUserId(Long userId);

}
