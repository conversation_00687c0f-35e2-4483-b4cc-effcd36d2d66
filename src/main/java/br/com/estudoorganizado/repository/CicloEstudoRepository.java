package br.com.estudoorganizado.repository;

import br.com.estudoorganizado.model.CicloEstudo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface CicloEstudoRepository extends JpaRepository<CicloEstudo, Long> {

    List<CicloEstudo> findByUserId(Long userId);

    List<CicloEstudo> findByUserIdAndConcluidoTrue(Long userId);

    Optional<CicloEstudo> findTopByUserIdOrderByIdDesc(Long userId);

    Optional<CicloEstudo> findByIdAndUserId(Long id, Long userId);

    boolean existsByIdAndUserId(Long id, Long userId);

}
