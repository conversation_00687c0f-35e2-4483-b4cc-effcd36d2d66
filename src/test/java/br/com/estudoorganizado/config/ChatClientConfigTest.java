package br.com.estudoorganizado.config;

import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotSame;

@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {"utilizar-spring-ai=true"})
public class ChatClientConfigTest {

    private final ChatClient chatClient;
    private final ChatClient contentGenerationChatClient;

    public ChatClientConfigTest(
            ChatClient chatClient,
            @Qualifier("contentGenerationChatClient") ChatClient contentGenerationChatClient) {
        this.chatClient = chatClient;
        this.contentGenerationChatClient = contentGenerationChatClient;
    }

    @Test
    public void testChatClientBeansAreCreated() {
        // Verifica se ambos os beans foram criados
        assertNotNull(chatClient, "ChatClient principal deve ser criado");
        assertNotNull(contentGenerationChatClient, "ChatClient para geração de conteúdo deve ser criado");
    }

    @Test
    public void testChatClientBeansAreDifferent() {
        // Verifica se são instâncias diferentes
        assertNotSame(chatClient, contentGenerationChatClient, 
            "ChatClient principal e de geração de conteúdo devem ser instâncias diferentes");
    }
}
