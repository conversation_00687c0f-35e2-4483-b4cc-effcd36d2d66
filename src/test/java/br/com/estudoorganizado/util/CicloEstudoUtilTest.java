package br.com.estudoorganizado.util;

import br.com.estudoorganizado.dto.CicloEstudoDisciplinaDTO;
import br.com.estudoorganizado.dto.DisciplinaDTO;
import org.junit.jupiter.api.Test;

import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CicloEstudoUtilTest {

    @Test
    void testGerarCicloEstudo_ComTresDisciplinas() {
        // Arrange
        List<CicloEstudoDisciplinaDTO> disciplinas = criarDisciplinasExemplo();
        double horasSemanais = 10.0; // 600 minutos
        int duracaoMaximaSessao = 90; // 90 minutos

        // Act
        CicloEstudoUtil.gerarCicloEstudo(disciplinas, horasSemanais, duracaoMaximaSessao);

        // Assert
        // Verificar se todas as disciplinas têm tempo e ordem definidos
        for (CicloEstudoDisciplinaDTO disciplina : disciplinas) {
            assertNotNull(disciplina.getTempoEstudoMeta(),
                    "Disciplina " + disciplina.getDisciplina().getNome() + " deve ter tempo de estudo meta");
            assertNotNull(disciplina.getOrdem(),
                    "Disciplina " + disciplina.getDisciplina().getNome() + " deve ter ordem definida");

            // Verificar se o tempo não é zero
            LocalTime tempo = disciplina.getTempoEstudoMeta();
            int minutos = tempo.getHour() * 60 + tempo.getMinute();
            assertTrue(minutos > 0,
                    "Disciplina " + disciplina.getDisciplina().getNome() + " deve ter tempo > 0");
        }

        // Verificar se todas as disciplinas têm ordem única
        long ordensUnicas = disciplinas.stream().mapToInt(CicloEstudoDisciplinaDTO::getOrdem).distinct().count();
        assertEquals(3, ordensUnicas, "Todas as disciplinas devem ter ordens únicas");
    }

    @Test
    void testGerarCicloEstudo_DistribuicaoPorPesoENivel() {
        // Arrange
        List<CicloEstudoDisciplinaDTO> disciplinas = criarDisciplinasExemplo();
        double horasSemanais = 10.0;
        int duracaoMaximaSessao = 90;

        // Act
        CicloEstudoUtil.gerarCicloEstudo(disciplinas, horasSemanais, duracaoMaximaSessao);

        // Assert
        CicloEstudoDisciplinaDTO portugues = disciplinas.stream()
                .filter(d -> "Português".equals(d.getDisciplina().getNome()))
                .findFirst().orElse(null);
        CicloEstudoDisciplinaDTO matematica = disciplinas.stream()
                .filter(d -> "Matemática".equals(d.getDisciplina().getNome()))
                .findFirst().orElse(null);

        assertNotNull(portugues);
        assertNotNull(matematica);

        // Português (peso 5, nível 2) deve ter mais tempo que Matemática (peso 3, nível 4)
        int tempoPortugues = portugues.getTempoEstudoMeta().getHour() * 60 + portugues.getTempoEstudoMeta().getMinute();
        int tempoMatematica = matematica.getTempoEstudoMeta().getHour() * 60 + matematica.getTempoEstudoMeta().getMinute();
        assertTrue(tempoPortugues > tempoMatematica,
                   "Português deve ter mais tempo que Matemática devido ao maior peso e menor nível");
    }

    @Test
    void testGerarCicloEstudo_ComListaVazia() {
        // Arrange
        List<CicloEstudoDisciplinaDTO> disciplinas = Arrays.asList();
        double horasSemanais = 10.0;
        int duracaoMaximaSessao = 90;

        // Act & Assert - não deve lançar exceção
        assertDoesNotThrow(() -> {
            CicloEstudoUtil.gerarCicloEstudo(disciplinas, horasSemanais, duracaoMaximaSessao);
        });
    }

    @Test
    void testGerarCicloEstudo_ComUmaDisciplina() {
        // Arrange
        CicloEstudoDisciplinaDTO disciplina = criarDisciplina("Direito Constitucional", 4, 3);
        List<CicloEstudoDisciplinaDTO> disciplinas = Arrays.asList(disciplina);
        double horasSemanais = 5.0; // 300 minutos
        int duracaoMaximaSessao = 90;

        // Act
        CicloEstudoUtil.gerarCicloEstudo(disciplinas, horasSemanais, duracaoMaximaSessao);

        // Assert
        assertNotNull(disciplina.getTempoEstudoMeta());
        assertNotNull(disciplina.getOrdem());
        assertEquals(1, disciplina.getOrdem().intValue());

        // Verificar se o tempo foi definido
        LocalTime tempo = disciplina.getTempoEstudoMeta();
        int minutos = tempo.getHour() * 60 + tempo.getMinute();
        assertTrue(minutos > 0, "Disciplina deve ter tempo > 0");
    }

    private List<CicloEstudoDisciplinaDTO> criarDisciplinasExemplo() {
        return Arrays.asList(
                criarDisciplina("Português", 5, 2),      // Alta prioridade (peso alto, nível baixo)
                criarDisciplina("Matemática", 3, 4),     // Baixa prioridade (peso médio, nível alto)
                criarDisciplina("Direito Administrativo", 4, 3) // Prioridade média
        );
    }

    private CicloEstudoDisciplinaDTO criarDisciplina(String nome, int peso, int nivelConhecimento) {
        DisciplinaDTO disciplinaDTO = new DisciplinaDTO();
        disciplinaDTO.setNome(nome);

        CicloEstudoDisciplinaDTO cicloEstudoDisciplinaDTO = new CicloEstudoDisciplinaDTO();
        cicloEstudoDisciplinaDTO.setDisciplina(disciplinaDTO);
        cicloEstudoDisciplinaDTO.setPeso(peso);
        cicloEstudoDisciplinaDTO.setNivelConhecimento(nivelConhecimento);

        return cicloEstudoDisciplinaDTO;
    }
}
