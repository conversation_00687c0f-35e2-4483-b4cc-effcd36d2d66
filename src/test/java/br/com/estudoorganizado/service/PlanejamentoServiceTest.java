package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.CicloEstudoDTO;
import br.com.estudoorganizado.dto.CicloEstudoDisciplinaDTO;
import br.com.estudoorganizado.dto.DisciplinaDTO;
import br.com.estudoorganizado.dto.PlanejamentoDTO;
import br.com.estudoorganizado.repository.PlanejamentoRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class PlanejamentoServiceTest {

    @Mock
    private PlanejamentoRepository planejamentoRepository;

    @Mock
    private CicloEstudoService cicloEstudoService;

    @Mock
    private ModelMapper modelMapper;

    @InjectMocks
    private PlanejamentoService planejamentoService;

    private PlanejamentoDTO planejamentoDTO;

    @BeforeEach
    void setUp() {
        planejamentoDTO = criarPlanejamentoExemplo();
    }

    @Test
    void testSugerirPlanejamentoCicloEstudo_ComTresDisciplinas() {
        // Act
        PlanejamentoDTO resultado = planejamentoService.sugerirPlanejamentoCicloEstudo(planejamentoDTO);

        // Assert
        assertNotNull(resultado);
        assertNotNull(resultado.getCicloEstudo());
        assertNotNull(resultado.getCicloEstudo().getDisciplinas());
        assertEquals(3, resultado.getCicloEstudo().getDisciplinas().size());

        // Verificar se todas as disciplinas têm tempo de estudo meta definido
        for (CicloEstudoDisciplinaDTO disciplina : resultado.getCicloEstudo().getDisciplinas()) {
            assertNotNull(disciplina.getTempoEstudoMeta(), 
                    "Disciplina " + disciplina.getDisciplina().getNome() + " deve ter tempo de estudo meta");
            assertNotNull(disciplina.getOrdem(), 
                    "Disciplina " + disciplina.getDisciplina().getNome() + " deve ter ordem definida");
        }
    }

    @Test
    void testSugerirPlanejamentoCicloEstudo_DistribuicaoProporcionaoPorPeso() {
        // Act
        PlanejamentoDTO resultado = planejamentoService.sugerirPlanejamentoCicloEstudo(planejamentoDTO);

        // Assert
        List<CicloEstudoDisciplinaDTO> disciplinas = resultado.getCicloEstudo().getDisciplinas();
        
        // Encontrar disciplinas por nome
        CicloEstudoDisciplinaDTO portugues = disciplinas.stream()
                .filter(d -> "Português".equals(d.getDisciplina().getNome()))
                .findFirst().orElse(null);
        
        CicloEstudoDisciplinaDTO matematica = disciplinas.stream()
                .filter(d -> "Matemática".equals(d.getDisciplina().getNome()))
                .findFirst().orElse(null);

        assertNotNull(portugues);
        assertNotNull(matematica);

        // Português (peso 5, nível 2) deve ter tempo >= Matemática (peso 3, nível 4)
        LocalTime tempoPortugues = portugues.getTempoEstudoMeta();
        LocalTime tempoMatematica = matematica.getTempoEstudoMeta();
        
        assertNotNull(tempoPortugues);
        assertNotNull(tempoMatematica);
        
        // Converter para minutos para comparação
        int minutosPortugues = tempoPortugues.getHour() * 60 + tempoPortugues.getMinute();
        int minutosMatematica = tempoMatematica.getHour() * 60 + tempoMatematica.getMinute();
        
        assertTrue(minutosPortugues >= minutosMatematica, 
                "Português deve ter tempo maior ou igual a Matemática devido ao maior peso e menor nível");
    }

    @Test
    void testSugerirPlanejamentoCicloEstudo_ComParametrosPersonalizados() {
        // Arrange
        planejamentoDTO.setHorasDisponiveisPorSemana(15); // 15 horas
        planejamentoDTO.setMinutosDuracaoMaximaPorSessao(120); // 2 horas

        // Act
        PlanejamentoDTO resultado = planejamentoService.sugerirPlanejamentoCicloEstudo(planejamentoDTO);

        // Assert
        assertNotNull(resultado);
        
        // Verificar se o resultado considera os novos parâmetros
        // (O tempo total distribuído deve ser proporcional às 15 horas)
        List<CicloEstudoDisciplinaDTO> disciplinas = resultado.getCicloEstudo().getDisciplinas();
        
        for (CicloEstudoDisciplinaDTO disciplina : disciplinas) {
            assertNotNull(disciplina.getTempoEstudoMeta());
            
            // Nenhuma sessão individual deve exceder 120 minutos
            LocalTime tempo = disciplina.getTempoEstudoMeta();
            int minutos = tempo.getHour() * 60 + tempo.getMinute();
            assertTrue(minutos <= 120, 
                    "Tempo por sessão não deve exceder 120 minutos para " + disciplina.getDisciplina().getNome());
        }
    }

    @Test
    void testSugerirPlanejamentoCicloEstudo_ComDisciplinasNulas() {
        // Arrange
        planejamentoDTO.setCicloEstudo(null);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            planejamentoService.sugerirPlanejamentoCicloEstudo(planejamentoDTO);
        });

        assertTrue(exception.getMessage().contains("Disciplinas não informadas"));
    }

    @Test
    void testSugerirPlanejamentoCicloEstudo_ComListaDisciplinasVazia() {
        // Arrange
        planejamentoDTO.getCicloEstudo().setDisciplinas(Arrays.asList());

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            planejamentoService.sugerirPlanejamentoCicloEstudo(planejamentoDTO);
        });

        assertTrue(exception.getMessage().contains("Disciplinas não informadas"));
    }

    @Test
    void testSugerirPlanejamentoCicloEstudo_ComValoresPadrao() {
        // Arrange - Remover valores para testar defaults
        planejamentoDTO.setHorasDisponiveisPorSemana(null);
        planejamentoDTO.setMinutosDuracaoMaximaPorSessao(null);

        // Act
        PlanejamentoDTO resultado = planejamentoService.sugerirPlanejamentoCicloEstudo(planejamentoDTO);

        // Assert
        assertNotNull(resultado);
        assertNotNull(resultado.getCicloEstudo().getDisciplinas());
        
        // Deve funcionar com valores padrão (10 horas, 90 minutos)
        for (CicloEstudoDisciplinaDTO disciplina : resultado.getCicloEstudo().getDisciplinas()) {
            assertNotNull(disciplina.getTempoEstudoMeta());
            assertNotNull(disciplina.getOrdem());
        }
    }

    private PlanejamentoDTO criarPlanejamentoExemplo() {
        PlanejamentoDTO dto = new PlanejamentoDTO();
        dto.setNome("Planejamento Concurso Público");
        dto.setHorasDisponiveisPorSemana(10);
        dto.setMinutosDuracaoMaximaPorSessao(90);

        CicloEstudoDTO cicloEstudo = new CicloEstudoDTO();
        cicloEstudo.setNome("Ciclo Preparatório");
        cicloEstudo.setDisciplinas(Arrays.asList(
                criarDisciplina("Português", 5, 2),
                criarDisciplina("Matemática", 3, 4),
                criarDisciplina("Direito Administrativo", 4, 3)
        ));

        dto.setCicloEstudo(cicloEstudo);
        return dto;
    }

    private CicloEstudoDisciplinaDTO criarDisciplina(String nome, int peso, int nivelConhecimento) {
        DisciplinaDTO disciplinaDTO = new DisciplinaDTO();
        disciplinaDTO.setNome(nome);

        CicloEstudoDisciplinaDTO cicloEstudoDisciplinaDTO = new CicloEstudoDisciplinaDTO();
        cicloEstudoDisciplinaDTO.setDisciplina(disciplinaDTO);
        cicloEstudoDisciplinaDTO.setPeso(peso);
        cicloEstudoDisciplinaDTO.setNivelConhecimento(nivelConhecimento);

        return cicloEstudoDisciplinaDTO;
    }
}
