# Guia de Configuração do Vertex AI com Spring AI

## 1. Configuração do Google Cloud

### Pré-requisitos
- Conta no Google Cloud Platform
- Projeto criado no GCP
- Vertex AI API habilitada

### Passos:

1. **Criar um projeto no Google Cloud Console**
   - Acesse https://console.cloud.google.com/
   - Crie um novo projeto ou selecione um existente

2. **Habilitar a Vertex AI API**
   ```bash
   gcloud services enable aiplatform.googleapis.com
   ```

3. **Criar uma Service Account**
   ```bash
   gcloud iam service-accounts create vertex-ai-service-account \ --description="Service account for Vertex AI" \ --display-name="Vertex AI Service Account"
   ```

4. **Conceder permissões necessárias**
   ```bash
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:vertex-ai-service-account@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/aiplatform.user"
   ```

5. **<PERSON><PERSON><PERSON> e baixar a chave JSON**
   ```bash
   gcloud iam service-accounts keys create vertex-ai-key.json \
     --iam-account=vertex-ai-service-account@YOUR_PROJECT_ID.iam.gserviceaccount.com
   ```

## 2. Configuração da Aplicação

### Opção 1: Usando variável de ambiente (Recomendado)

1. **Definir a variável de ambiente**
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/vertex-ai-key.json"
   ```

2. **Configurar application.properties**
   ```properties
   spring.ai.vertex.ai.gemini.project-id=YOUR_PROJECT_ID
   spring.ai.vertex.ai.gemini.location=us-central1
   spring.ai.vertex.ai.gemini.model=gemini-1.5-pro
   spring.ai.vertex.ai.gemini.temperature=0.7
   spring.ai.vertex.ai.gemini.max-output-tokens=2048
   ```

### Opção 2: Usando configuração direta (Não recomendado para produção)

```properties
spring.ai.vertex.ai.gemini.project-id=YOUR_PROJECT_ID
spring.ai.vertex.ai.gemini.location=us-central1
spring.ai.vertex.ai.gemini.credentials-uri=file:///path/to/vertex-ai-key.json
```

## 3. Testando a Configuração

1. **Iniciar a aplicação**
   ```bash
   ./mvnw spring-boot:run
   ```

2. **Testar o endpoint de health**
   ```bash
   curl http://localhost:8080/v1/vertex-ai/health
   ```

3. **Testar comunicação simples**
   ```bash
   curl http://localhost:8080/v1/vertex-ai/test-simple
   ```

## 4. Troubleshooting

### Erro: "ChatModel bean not found"
- Verifique se as propriedades `spring.ai.vertex.ai.gemini.*` estão configuradas
- Confirme que a variável `GOOGLE_APPLICATION_CREDENTIALS` está definida
- Verifique se o arquivo de credenciais existe e tem as permissões corretas

### Erro: "Authentication failed"
- Verifique se a Service Account tem as permissões necessárias
- Confirme se a Vertex AI API está habilitada no projeto
- Teste as credenciais usando gcloud CLI

### Erro: "Project not found"
- Verifique se o project-id está correto
- Confirme se você tem acesso ao projeto no GCP

## 5. Segurança

- **Nunca** commite arquivos de credenciais no repositório
- Use variáveis de ambiente ou serviços de gerenciamento de secrets
- Rotacione as chaves regularmente
- Use o princípio do menor privilégio para as permissões

## 6. Modelos Disponíveis

- `gemini-1.5-pro`: Modelo mais avançado, melhor para tarefas complexas
- `gemini-1.5-flash`: Modelo mais rápido, adequado para tarefas simples
- `gemini-1.0-pro`: Versão anterior, ainda funcional

## 7. Limites e Quotas

- Verifique os limites de quota no Google Cloud Console
- Configure alertas de billing para evitar custos inesperados
- Monitore o uso através do Cloud Monitoring
