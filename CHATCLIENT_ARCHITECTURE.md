# Arquitetura ChatClient - Solução para Loop Infinito

## Problema Identificado

O sistema estava enfrentando um **loop infinito** na criação de planejamentos com IA devido à seguinte sequência:

1. **ChatClient configurado com funções padrão**: O ChatClient principal tinha as funções `criarPlanejamentoComIA`, `cadastrarDisciplina` e `salvarOuAtualizarPlanejamento` configuradas
2. **Usuário solicita criação de planejamento**: O ChatClient identifica que deve usar a função `criarPlanejamentoComIA`
3. **Função chama o serviço**: A função na `FuncoesIAConfig` chama o `PlanejamentoInteligentService.criarPlanejamentoComIA()`
4. **Serviço usa o mesmo ChatClient**: O serviço usava o mesmo ChatClient (com funções) para gerar o conteúdo
5. **Loop infinito**: O ChatClient identifica novamente que deve usar a função `criarPlanejamentoComIA`

## Solução Implementada

### 1. Separação de Responsabilidades

Criamos **dois ChatClients distintos** com responsabilidades específicas:

#### ChatClient Principal (`chatClient`)
- **Propósito**: Interações conversacionais com o usuário
- **Configuração**: Inclui funções padrão para executar ações
- **Usado em**: `AssistenteIAController` para chat conversacional
- **Funções disponíveis**: 
  - `cadastrarDisciplina`
  - `criarPlanejamentoComIA` 
  - `salvarOuAtualizarPlanejamento`

#### ChatClient de Geração de Conteúdo (`contentGenerationChatClient`)
- **Propósito**: Geração pura de conteúdo (JSON, textos estruturados)
- **Configuração**: SEM funções, apenas prompt system especializado
- **Usado em**: `PlanejamentoInteligentService` para gerar planejamentos
- **Características**: Focado em retornar JSON válido

### 2. Arquivos Modificados

#### `ChatClientConfig.java`
```java
@Bean
public ChatClient chatClient(ChatClient.Builder chatClientBuilder) {
    return chatClientBuilder
        .defaultFunctions("cadastrarDisciplina", "criarPlanejamentoComIA", "salvarOuAtualizarPlanejamento")
        .defaultSystem("Assistente conversacional...")
        .build();
}

@Bean
@Qualifier("contentGenerationChatClient")
public ChatClient contentGenerationChatClient(ChatClient.Builder chatClientBuilder) {
    return chatClientBuilder
        .defaultSystem("Especialista em planejamento de estudos...")
        .build();
}
```

#### `PlanejamentoInteligentService.java`
- Modificado para usar `@Qualifier("contentGenerationChatClient")`
- Injeção de dependência via construtor
- Uso do ChatClient específico para geração de conteúdo

#### `TestConfig.java`
- Adicionado mock para o novo ChatClient
- Mantém compatibilidade com testes existentes

### 3. Fluxo Corrigido

```
Usuário solicita planejamento
    ↓
ChatClient principal (com funções) → FuncoesIAConfig.criarPlanejamentoComIA()
    ↓
PlanejamentoInteligentService.criarPlanejamentoComIA()
    ↓
ChatClient de geração (SEM funções) → Gera JSON do planejamento
    ↓
Retorna planejamento estruturado
```

## Benefícios da Solução

1. **Elimina o loop infinito**: ChatClient de geração não tem funções configuradas
2. **Separação clara de responsabilidades**: Cada ChatClient tem um propósito específico
3. **Mantém funcionalidade conversacional**: ChatClient principal continua com todas as funções
4. **Flexibilidade**: Permite diferentes configurações para diferentes casos de uso
5. **Testabilidade**: Mocks separados para cada tipo de ChatClient

## Uso Recomendado

### Para Interações Conversacionais
```java
@Autowired
private ChatClient chatClient; // Usa o principal com funções
```

### Para Geração de Conteúdo
```java
@Autowired
@Qualifier("contentGenerationChatClient")
private ChatClient contentGenerationChatClient; // Usa o específico sem funções
```

## Testes

Criado `ChatClientConfigTest.java` para verificar:
- Ambos os beans são criados corretamente
- São instâncias diferentes
- Configuração está funcionando

## Próximos Passos

1. Executar testes para validar a solução
2. Testar criação de planejamento via API
3. Verificar se o chat conversacional continua funcionando
4. Monitorar logs para confirmar ausência de loops
